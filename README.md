## Builds: Python
## Deployments: OpenShift Pipeline
## 

## Support Documentation

- [ ] [Repository Design](https://confluence.fleetcor.com/display/CTO/GCTO+DevOps+Pipelines+-+Repository+Requirements)
- [ ] [GCTO DevSecOps Support Page](https://confluence.fleetcor.com/pages/viewpage.action?pageId=69641600)

## Setting up your repository

```
cd existing_repo
git remote add origin https://git.fleetcor.com/path/to/repository.git
git branch -M main
git push -uf origin main
```

### Variables to  define 
- $RUNNER_TAG 
- $DEV_OCP_API - URL to the OCP API
- $DEV_OCP_TOKEN - Login token for OCP
- $DEV_NAMESPACE - The namespace
- $QA_OCP_API - URL to the OCP API
- $QA_OCP_TOKEN - Login token for OCP
- $QA_NAMESPACE - The namespace (parent application group)

## GCTO Developer Pipelines

- [ ] [GCTO Pipeline Templates](https://git.fleetcor.com/gcto-pipelines/pipeline-templates/templates)

## To create nest module from command line
We can use this script to generate the nest module

- Example to create a alerts module:
- It will create the alerts module, controller and service inside the modules folder
  
``` 
./create-nest-module.sh alerts
``` 
 
Steps:
 - Install CLI
``` 
sudo npm install -g @nestjs/cli
```

- Give permission (For Mac users)
```
chmod +x create-nest-module.sh
```

- Create a new module along with controller, service and module
```
./create-nest-module.sh user
```

- To create only module
```
nest g mo user
```

- To create only controller
```
nest g co user
```

- To create only service
```
nest g s user
```
