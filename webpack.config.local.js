// const {IgnorePlugin } = require('webpack');
const webpack = require('webpack');
const path = require('path');
const nodeExternals = require('webpack-node-externals');
const { RunScriptWebpackPlugin } = require('run-script-webpack-plugin');

module.exports = {
  entry: ['webpack/hot/poll?100', './src/main.ts'],
  target: 'node',
  externals: [
    nodeExternals({
      allowlist: ['webpack/hot/poll?100'],
    }),
  ],
  module: {
    rules: [
      {
        test: /.tsx?$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
    ],
  },
  mode: process.env.APP_ENV === 'prod' ? 'production' : 'development',
  resolve: {
    extensions: ['.*', '.tsx', '.ts', '.js'],
  },
  plugins: [
    new webpack.HotModuleReplacementPlugin(),
    new RunScriptWebpackPlugin({ name: 'main.js', autoRestart: false }),
    // new webpack.DefinePlugin({
    //     'process.env.BUILD_DATE': JSON.stringify(date)
    //   }),
    new webpack.IgnorePlugin({
        checkResource(resource) {
          const lazyImports = [
            '@nestjs/platform-express',
            '@nestjs/websockets/socket-module',
            'amqp-connection-manager',
            'amqplib',
            'cache-manager',
            'cache-manager/package.json',
            'class-transformer/storage',
            'hbs',
            'ioredis',
            'kafkajs',
            'mqtt',
            'nats',
            'utf-8-validate',
            'bufferutil'
          ];
          if (!lazyImports.includes(resource)) {
            return false;
          }
          try {
            require.resolve(resource, { paths: [process.cwd()] });
          } catch (err) {
            return true;
          }
          return false;
        },
      }),
  ],
  output: {
    path: path.resolve('build'),
    filename: 'main.js',
  },
};