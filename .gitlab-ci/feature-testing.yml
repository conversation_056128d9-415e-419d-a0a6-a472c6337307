Feature Testing:
  except:
    - main
    - master
    - tags
  stage: Deploy
  environment:
    name: testing
  artifacts:
    paths:
      - artifacts/*
    expire_in: 2 weeks
  when: manual
  needs:
    - Package Services
  image: $OCP_DEPLOY_IMAGE
  tags:
    - $RUNNER_TAG
  before_script:
    - export TAG="${CI_COMMIT_TAG:=$CI_COMMIT_BRANCH-$CI_COMMIT_SHORT_SHA}"
    - export TAG="${TAG//\//_}" # replace all / with _
    - export REVISION="$TAG.$CI_PIPELINE_IID"
    - export REGISTRY_IMAGE="$CI_REGISTRY_IMAGE:$REVISION"

    - |
      if [[ "$CI_COMMIT_TAG" =~ ^hotfix- || "$CI_COMMIT_BRANCH" =~ ^hotfix- ]]; then
        PROJECT_NAME="$CI_PROJECT_NAME-hotfix"
      else
        PROJECT_NAME="$CI_PROJECT_NAME"
      fi

      sed -i "
        s,build_image,$REGISTRY_IMAGE,g
        s,build_version,$PROJECT_NAME-$TAG,g
        s,ci_project_name,$PROJECT_NAME,g
        s,ci_namespace,$QA_NAMESPACE,g
      " $CI_PROJECT_DIR/deploy/qa/*

    - echo $QA_OCP_API
    - /app/oc login --server="$QA_OCP_API" --token="$QA_OCP_TOKEN" --insecure-skip-tls-verify
    - /app/oc project "$QA_NAMESPACE" 2> /dev/null

  script:
    - test -f $CI_PROJECT_DIR/deploy/qa/SealedSecrets-app.yaml && /app/oc delete -f $CI_PROJECT_DIR/deploy/qa/SealedSecrets-app.yaml || true
    - test -f $CI_PROJECT_DIR/deploy/qa/SealedSecrets-app.yaml && /app/oc create -f $CI_PROJECT_DIR/deploy/qa/SealedSecrets-app.yaml || true
    - test -f $CI_PROJECT_DIR/deploy/qa/ConfigMap.yaml && /app/oc apply -f $CI_PROJECT_DIR/deploy/qa/ConfigMap.yaml || true
    - test -f $CI_PROJECT_DIR/deploy/qa/Service.yaml && /app/oc apply -f $CI_PROJECT_DIR/deploy/qa/Service.yaml || true
    - test -f $CI_PROJECT_DIR/deploy/qa/Route.yaml && /app/oc apply -f $CI_PROJECT_DIR/deploy/qa/Route.yaml || true
    - test -f $CI_PROJECT_DIR/deploy/qa/Deployment.yaml && /app/oc apply -f $CI_PROJECT_DIR/deploy/qa/Deployment.yaml || true
    - test -f $CI_PROJECT_DIR/deploy/qa/CronJob.yaml && /app/oc apply -f $CI_PROJECT_DIR/deploy/qa/CronJob.yaml || true
  allow_failure: false
