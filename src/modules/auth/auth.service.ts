import { Injectable, Inject } from '@nestjs/common';
import { TokenRequest } from './dto/token.dto';
import { AxiosResponse } from 'axios';
import * as OktaJwtVerifier from '@okta/jwt-verifier';
import { IToken } from './interfaces/token.interface';
import { AppLogger } from '../../core/logger/logger.service';
import { MWHttpService } from '../../core/service/http.service';
import { token } from '../../utils/constants';

@Injectable()
export class AuthService implements IToken {

    constructor(private readonly httpService: MWHttpService, private readonly appLogger: AppLogger) { }

    //This can be rollback later
    // async getAccessToken(request: TokenRequest): Promise<AxiosResponse<any>> {
    //     this.appLogger.log("getAccessToken request");
    //     const auth = Buffer.from(`${request.client_id}:${request.client_secret}`).toString('base64');
    //     const okatInstance = process.env.OKTA_INSTANCE + '/v1/token';
    //     const headers = {
    //         'Authorization': `Basic ${auth}`,
    //         'Content-Type': 'application/x-www-form-urlencoded'
    //     };
    //     const data = `grant_type=${request.grant_type}&scope=${process.env.OKTA_SCOPE}`;
    //     const response = await this.httpService.axiosRef.post(okatInstance, data, { headers });
    //     return response.data;
    // }

    async getOktaAppToken<T>(request: TokenRequest): Promise<T> {
        this.appLogger.log("GetOktaAccessToken request");
        const endpoint = process.env.SECURITY_API + token;
        const data = JSON.stringify(request);
        const config = this.httpService.createRequestConfig('POST', endpoint, data, {}, {});
        return this.httpService.makeRequest(config);
    }

    async validateToken(token: string): Promise<OktaJwtVerifier.Jwt> {
        const oktaJwtVerifier = new OktaJwtVerifier({
            issuer: process.env.OKTA_INSTANCE as string, // issuer required
            clientId: process.env.OKTA_CLIENT_ID as string,
        });
        try {
            const jwt = await oktaJwtVerifier.verifyAccessToken(token, "Client Application");
            return jwt;
        } catch (error) {
            this.appLogger.error("Error validating token:", error);
            throw error;
        }
    }
}
