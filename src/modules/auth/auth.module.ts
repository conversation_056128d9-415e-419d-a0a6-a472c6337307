import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { HttpModule } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { AppLogger } from '../../core/logger/logger.service';
import { MWHttpService } from '../../core/service/http.service';
@Module({
    controllers: [AuthController],
    providers: [AuthService, ConfigService, AppLogger, Logger, MWHttpService],
    imports: [
        HttpModule.registerAsync({
        useFactory: () => ({
            timeout: 10000,
            maxRedirects: 5,
        }),
    }),]
})
export class AuthModule { }
