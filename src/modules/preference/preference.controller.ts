import { Body, Controller, Headers, Post, UseGuards, UseInterceptors, UsePipes, ValidationPipe } from '@nestjs/common';

import { ValidHeadersGuard } from '../../core/guards/valid.headers.guard';
import { LoggingInterceptor } from '../../core/interceptors/logging.interceptor';
import { TimeoutInterceptor } from '../../core/interceptors/timeout.interceptor';
import { SetCmUserPreferenceRequest } from './dto/set.cmuser.preference.request';
import { PreferenceService } from './preference.service';
import { HeaderDTO } from '../../core/dto/header_dto';

@Controller({ version: '1', path: 'preferences' })
@UseGuards(ValidHeadersGuard)
@UseInterceptors(TimeoutInterceptor, LoggingInterceptor)
@UsePipes(new ValidationPipe({ transform: true }))
export class PreferenceController {
    constructor(private readonly preferenceService: PreferenceService) { }

    @Post('setCMUserPreferences')
    async setCmUserPreference(@Headers() headers: HeaderDTO, @Body() request: SetCmUserPreferenceRequest) {
        return await this.preferenceService.setCmUserPreference(headers, request);
    }

    @Post('getCMUserPreferences')
    async getCMUserPreferences(@Headers() headers: HeaderDTO) {
        return await this.preferenceService.getCMUserPreferences(headers);
    }
}
