import { Module } from '@nestjs/common';

import { AppLogger } from '../../core/logger/logger.service';
import { MWHttpService } from '../../core/service/http.service';
import { UtilityService } from '../../utils/utility-service';
import { AuthService } from '../auth/auth.service';
import { PreferenceController } from './preference.controller';
import { PreferenceService } from './preference.service';

@Module({
  controllers: [PreferenceController],
  providers: [PreferenceService, MWHttpService, AppLogger, AuthService, UtilityService],
})
export class PreferenceModule { }
