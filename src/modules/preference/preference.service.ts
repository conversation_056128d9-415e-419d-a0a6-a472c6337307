import { Injectable } from '@nestjs/common';

import { MWHttpService } from '../../core/service/http.service';
import { getUrl } from '../../utils/api-routes';
import { getCMUserPreferences, setCMUserPreferences } from '../../utils/constants';
import { MethodType } from '../../utils/method-type';
import { UtilityService } from '../../utils/utility-service';
import { SetCmUserPreferenceRequest } from './dto/set.cmuser.preference.request';
import { HeaderDTO } from '../../core/dto/header_dto';

@Injectable()
export class PreferenceService {
    constructor(private readonly httpService: MWHttpService,
        private readonly utilityService: UtilityService) { }

    async setCmUserPreference<T>(headers: HeaderDTO, request: SetCmUserPreferenceRequest) {
        this.utilityService.logRequest<T>('setCmUserPreference', request);
        const url = getUrl(setCMUserPreferences);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async getCMUserPreferences<T>(headers: HeaderDTO) {
        this.utilityService.logRequest<T>('getCMUserPreferences', {});
        const url = getUrl(getCMUserPreferences);
        return await this.httpService.processRequest(url, {}, headers, MethodType.POST);
    }
}
