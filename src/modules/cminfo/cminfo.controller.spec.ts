import { Test, TestingModule } from '@nestjs/testing';

import { CmInfoController } from './cminfo.controller';

describe('CmInfoController', () => {
    let controller: CmInfoController;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            controllers: [CmInfoController],
        }).compile();

        controller = module.get<CmInfoController>(CmInfoController);
    });

    it('should be defined', () => {
        expect(controller).toBeDefined();
    });
});
