import { Modu<PERSON> } from '@nestjs/common';

import { AppLogger } from '../../core/logger/logger.service';
import { MWHttpService } from '../../core/service/http.service';
import { UtilityService } from '../../utils/utility-service';
import { AuthService } from '../auth/auth.service';
import { CmInfoController } from './cminfo.controller';
import { CmInfoService } from './cminfo.service';

@Module({
    controllers: [CmInfoController],
    providers: [CmInfoService, MWHttpService, AppLogger, AuthService, UtilityService],
})
export class CmInfoModule { }
