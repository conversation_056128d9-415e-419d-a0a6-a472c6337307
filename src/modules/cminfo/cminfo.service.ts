import { Injectable } from '@nestjs/common';

import { MWHttpService } from '../../core/service/http.service';
import { getUrl } from '../../utils/api-routes';
import { getCMUserInfo } from '../../utils/constants';
import { MethodType } from '../../utils/method-type';
import { UtilityService } from '../../utils/utility-service';
import { HeaderDTO } from '../../core/dto/header_dto';

@Injectable()
export class CmInfoService {
    constructor(private readonly httpService: MWHttpService,
        private readonly utilityService: UtilityService,) { }

    async getCMUserInfo<T>(headers: HeaderDTO): Promise<T> {
        const request = { userId: headers.userid };
        this.utilityService.logRequest<T>('getCMUserInfo', request);
        const url = getUrl(getCMUserInfo);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

}
