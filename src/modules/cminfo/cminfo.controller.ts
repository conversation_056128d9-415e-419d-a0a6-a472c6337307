import { <PERSON>, Head<PERSON>, Post, UseGuards, UseInterceptors, UsePipes, ValidationPipe } from '@nestjs/common';

import { ValidHeadersGuard } from '../../core/guards/valid.headers.guard';
import { LoggingInterceptor } from '../../core/interceptors/logging.interceptor';
import { TimeoutInterceptor } from '../../core/interceptors/timeout.interceptor';
import { CmInfoService } from './cminfo.service';

@Controller({ version: '1', path: 'cminfo' })
@UseGuards(ValidHeadersGuard)
@UseInterceptors(TimeoutInterceptor, LoggingInterceptor)
@UsePipes(new ValidationPipe({ transform: true }))

export class CmInfoController {
    constructor(private readonly cmInfoService: CmInfoService) { }

    @Post('getCMUserInfo')
    async getCMUserInfo(@Headers() headers) {
        return await this.cmInfoService.getCMUserInfo(headers);
    }

}
