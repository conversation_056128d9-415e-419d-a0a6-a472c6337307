import { Body, Controller, Headers, Post, UseGuards, UseInterceptors, UsePipes, ValidationPipe } from '@nestjs/common';

import { HeaderDTO } from '../../core/dto/header_dto';
import { ValidHeadersGuard } from '../../core/guards/valid.headers.guard';
import { LoggingInterceptor } from '../../core/interceptors/logging.interceptor';
import { TimeoutInterceptor } from '../../core/interceptors/timeout.interceptor';
import { BankService } from './bank.service';
import { AddBankAccountRequest } from './dto/add.bank.account.request';
import { TransferToBankRequest } from './dto/transfer.to.bank.request';
import { ValidateBankAccountRequest } from './dto/validate.bank.account.request';

@Controller({ version: '1', path: 'bankManagement' })
@UseGuards(ValidHeadersGuard)
@UseInterceptors(TimeoutInterceptor, LoggingInterceptor)
@UsePipes(new ValidationPipe({ transform: true }))

export class BankController {
    constructor(private readonly bankService: BankService) { }

    @Post('addBankAccount')
    async addBankAccount(@Headers() headers: HeaderDTO, @Body() request: AddBankAccountRequest) {
        return await this.bankService.addBankAccount(headers, request);
    }

    @Post('getBankAccount')
    async getBankAccount(@Headers() headers) {
        return await this.bankService.getBankAccount(headers);
    }

    @Post('validateBankName')
    async validateBankName(@Headers() headers, @Body() request: ValidateBankAccountRequest) {
        return await this.bankService.validateBankName(headers, request);
    }

    @Post('transferToBank')
    async transferToBank(@Headers() headers: HeaderDTO, @Body() request: TransferToBankRequest) {
        return await this.bankService.transferToBank(headers, request);
    }
}
