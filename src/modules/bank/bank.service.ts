import { Injectable } from '@nestjs/common';

import { HeaderDTO } from '../../core/dto/header_dto';
import { MWHttpService } from '../../core/service/http.service';
import { getUrl } from '../../utils/api-routes';
import { addBankAccount, getBankAccount, transferToBank, validateBankName } from '../../utils/constants';
import { MethodType } from '../../utils/method-type';
import { UtilityService } from '../../utils/utility-service';
import { AddBankAccountRequest } from './dto/add.bank.account.request';
import { TransferToBankRequest } from './dto/transfer.to.bank.request';
import { ValidateBankAccountRequest } from './dto/validate.bank.account.request';

@Injectable()
export class BankService {
    constructor(private readonly httpService: MWHttpService,
        private readonly utilityService: UtilityService) { }

    async addBankAccount<T>(headers: HeaderDTO, request: AddBankAccountRequest): Promise<T> {
        this.utilityService.logRequest<T>('addBankAccount', request);
        const url = getUrl(addBankAccount);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async getBankAccount<T>(headers: HeaderDTO): Promise<T> {
        this.utilityService.logRequest<T>('getBankAccount', headers);
        const url = getUrl(getBankAccount);
        return await this.httpService.processRequest(url, {}, headers, MethodType.POST);
    }

    async validateBankName<T>(headers: HeaderDTO, request: ValidateBankAccountRequest): Promise<T> {
        this.utilityService.logRequest<T>('validateBankName', request);
        const url = getUrl(validateBankName);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async transferToBank<T>(headers: HeaderDTO, request: TransferToBankRequest): Promise<T> {
        this.utilityService.logRequest<T>('transferToBank', request);
        const url = getUrl(transferToBank);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

}
