import { Module } from '@nestjs/common';

import { AppLogger } from '../../core/logger/logger.service';
import { MWHttpService } from '../../core/service/http.service';
import { UtilityService } from '../../utils/utility-service';
import { AuthService } from '../auth/auth.service';
import { BankController } from './bank.controller';
import { BankService } from './bank.service';

@Module({
    controllers: [BankController],
    providers: [BankService, MWHttpService, AppLogger, AuthService, UtilityService],
})
export class BankModule { }
