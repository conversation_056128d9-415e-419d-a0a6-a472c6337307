import { Injectable } from "@nestjs/common";
import { HeaderDTO } from "../../core/dto/header_dto";
import { MWHttpService } from "../../core/service/http.service";
import { getCardMapiUrl } from "../../utils/api-routes";
import { fuelSessionMapi, updateFuelSessionLog, updateMobileDeviceTokenEndpoint as updateDeviceTokenEndpoint, updateMobileDeviceTokenLog as updateDeviceTokenLog } from "../../utils/constants";
import { MethodType } from "../../utils/method-type";
import { UtilityService } from "../../utils/utility-service";
import { MapiFuelSessionStartRequest } from "./dto/mapi.fuel.session.start.request";
import { MapiUpdateDeviceTokenRequest } from "./dto/mapi.update.device.token.request";

@Injectable()
export class MapiNotificationService {
    constructor(private readonly httpService: MWHttpService,
        private readonly utilityService: UtilityService,) { }

    //PN token update
    async updateFuelSessionOnSessionStart<T>(headers: HeaderDTO, mapiFuelSessionStartRequest: MapiFuelSessionStartRequest): Promise<T> {
        this.utilityService.logRequest<T>(updateFuelSessionLog, mapiFuelSessionStartRequest);
        const url = getCardMapiUrl(fuelSessionMapi);
        return await this.httpService.processRequest(url, mapiFuelSessionStartRequest, headers, MethodType.POST);
    }

    async updateDeviceToken<T>(headers: HeaderDTO, mapiUpdateDeviceTokenRequest: MapiUpdateDeviceTokenRequest): Promise<T> {
        this.utilityService.logRequest<T>(updateDeviceTokenLog, mapiUpdateDeviceTokenRequest);
        const url = getCardMapiUrl(updateDeviceTokenEndpoint);
        return await this.httpService.processRequest(url, mapiUpdateDeviceTokenRequest, headers, MethodType.PUT);
    }
}