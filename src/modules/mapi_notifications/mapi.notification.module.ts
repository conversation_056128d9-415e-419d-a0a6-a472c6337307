import { <PERSON>du<PERSON> } from "@nestjs/common";
import { AppLogger } from "../../core/logger/logger.service";
import { MWHttpService } from "../../core/service/http.service";
import { UtilityService } from "../../utils/utility-service";
import { AuthService } from "../auth/auth.service";
import { MapiNotificationController } from "./mapi.notification.controller";
import { MapiNotificationService } from "./mapi.notification.service";

@Module({
    controllers: [MapiNotificationController],
    providers: [MapiNotificationService, MWHttpService, AppLogger, AuthService, UtilityService],
})

export class MapiNotificationModule { }
