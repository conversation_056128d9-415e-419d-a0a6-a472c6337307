import { Body, Controller, Headers, Post, UseGuards, UseInterceptors, UsePipes, ValidationPipe } from "@nestjs/common";
import { HeaderDTO } from "../../core/dto/header_dto";
import { ValidHeadersGuard } from "../../core/guards/valid.headers.guard";
import { LoggingInterceptor } from "../../core/interceptors/logging.interceptor";
import { TimeoutInterceptor } from "../../core/interceptors/timeout.interceptor";
import { MapiFuelSessionStartRequest } from "./dto/mapi.fuel.session.start.request";
import { MapiUpdateDeviceTokenRequest } from "./dto/mapi.update.device.token.request";
import { MapiNotificationService } from "./mapi.notification.service";

@Controller({ version: '1', path: 'mapi' })
@UseGuards(ValidHeadersGuard)
@UseInterceptors(TimeoutInterceptor, LoggingInterceptor)
@UsePipes(new ValidationPipe({ transform: true }))

export class MapiNotificationController {

    constructor(private readonly notificationService: MapiNotificationService) { }

    @Post('notification/fuelSessionStart')
    async updateFuelSessionOnStart(@Headers() headers: HeaderDTO, @Body() request: MapiFuelSessionStartRequest): Promise<any> {
        return await this.notificationService.updateFuelSessionOnSessionStart(headers, request);
    }

    @Post('notification/updateDeviceToken')
    async updateMobileDeviceToken(@Headers() headers: HeaderDTO, @Body() request: MapiUpdateDeviceTokenRequest): Promise<any> {
        return await this.notificationService.updateDeviceToken(headers, request);
    }
}