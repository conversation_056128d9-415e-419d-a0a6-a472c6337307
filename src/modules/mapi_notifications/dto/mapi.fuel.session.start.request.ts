import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class MapiFuelSessionStartRequest {
    @ApiProperty()
    @IsNotEmpty()
    mobileDeviceToken: string;

    @ApiProperty()
    @IsNotEmpty()
    cardToken: string;

    @ApiProperty()
    @IsNotEmpty()
    fuelSessionType: string;

    @ApiProperty()
    @IsNotEmpty()
    userId: string;

    @ApiProperty()
    sessionStart: string;

    @ApiProperty()
    sessionEnd: string;

    @ApiProperty()
    @IsNotEmpty()
    sessionContextId: string;

    @ApiProperty()
    @IsNotEmpty()
    sysAccountId: string;

    @ApiProperty()
    @IsNotEmpty()
    unitNumber: String;

    @ApiProperty()
    appName: String;
}