import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class MapiAddCardStatusRequest {
    @ApiProperty()
    @IsNotEmpty()
    onBoardingId: string;

    @ApiProperty()
    @IsNotEmpty()
    status: string;

    @ApiProperty()
    @IsNotEmpty()
    fleetId: string;

    @ApiProperty()
    @IsNotEmpty()
    userId: string;

    @ApiProperty()
    @IsNotEmpty()
    cardToken: string;

    @ApiProperty()
    @IsNotEmpty()
    mobileDeviceToken: string;

    @ApiProperty()
    @IsNotEmpty()
    sysAccountId: string;

    constructor(partial: Partial<MapiAddCardStatusRequest>) {
        Object.assign(this, partial);
    }
}