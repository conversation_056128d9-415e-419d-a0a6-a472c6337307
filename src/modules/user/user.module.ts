import { Modu<PERSON> } from '@nestjs/common';

import { AppLogger } from '../../core/logger/logger.service';
import { MWHttpService } from '../../core/service/http.service';
import { UtilityService } from '../../utils/utility-service';
import { AuthService } from '../auth/auth.service';
import { UserController } from './user.controller';
import { UserService } from './user.service';

@Module({
  controllers: [UserController],
  providers: [UserService, MWHttpService, AppLogger, AuthService, UtilityService],
})
export class UserModule { }
