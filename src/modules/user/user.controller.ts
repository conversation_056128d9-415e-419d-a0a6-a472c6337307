import { Body, Controller, Headers, Patch, Post, UseGuards, UseInterceptors, UsePipes, ValidationPipe } from '@nestjs/common';

import { HeaderDTO } from '../../core/dto/header_dto';
import { ValidHeadersGuard } from '../../core/guards/valid.headers.guard';
import { LoggingInterceptor } from '../../core/interceptors/logging.interceptor';
import { TimeoutInterceptor } from '../../core/interceptors/timeout.interceptor';
import { UpdateCMUserInfoRequest } from './dto/update.cm.user.info.request';
import { UserService } from './user.service';

@Controller({ version: '1', path: 'user' })
@UseGuards(ValidHeadersGuard)
@UseInterceptors(TimeoutInterceptor, LoggingInterceptor)
@UsePipes(new ValidationPipe({ transform: true }))
export class UserController {

    constructor(private readonly userService: UserService) { }

    @Patch('deleteAccount')
    async deleteAccount(@Headers() headers) {
        return await this.userService.deleteAccount(headers);
    }

    @Post('updateCMUserInfo')
    async updateCMUserInfo(@Headers() headers: HeaderDTO, @Body() request: UpdateCMUserInfoRequest) {
        return await this.userService.updateCMUserInfo(headers, request);
    }
}
