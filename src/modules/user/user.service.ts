import { Injectable } from '@nestjs/common';

import { MWHttpService } from '../../core/service/http.service';
import { getUrl } from '../../utils/api-routes';
import { deleteAccount, updateCMUserInfo } from '../../utils/constants';
import { MethodType } from '../../utils/method-type';
import { UtilityService } from '../../utils/utility-service';
import { UpdateCMUserInfoRequest } from './dto/update.cm.user.info.request';
import { HeaderDTO } from '../../core/dto/header_dto';

@Injectable()
export class UserService {

    constructor(private readonly httpService: MWHttpService,
        private readonly utilityService: UtilityService,) { }

    async deleteAccount<T>(headers: HeaderDTO): Promise<T> {
        this.utilityService.logRequest<T>('deleteAccount', headers);
        const url = getUrl(deleteAccount);
        return await this.httpService.processRequest(url, {}, headers, MethodType.PATCH);
    }

    async updateCMUserInfo<T>(headers: HeaderDTO, request: UpdateCMUserInfoRequest): Promise<T> {
        this.utilityService.logRequest<T>('updateCMUserInfo', headers);
        const url = getUrl(updateCMUserInfo);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }
}
