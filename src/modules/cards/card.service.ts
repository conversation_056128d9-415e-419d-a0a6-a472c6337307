import { Injectable } from '@nestjs/common';

import { HeaderDTO } from '../../core/dto/header_dto';
import { MWHttpService } from '../../core/service/http.service';
import { getCardMapiUrl, getUrl } from '../../utils/api-routes';
import { AppInfo } from '../../utils/app.info';
import { addCardToWalletMapi, addCardToWalletV3, applyCard, autoLockUnlock, cardToCardTransfer, cardTypeV2, cardWallet, checkDrivenUser, cipStatus, deleteCardMapi, generatePINKey, getExpressCheckBalance, getUserDetails, moveExpressCheckBalanceToCard, onboardStatus, propCardWalletV2, propCardWalletV3, refreshWallet, registerComchekDraft, resetPin, sendMoney, setOnboardStatus, setOnboardStatusLog, statusRemoveCardMapi, suspendCardRequestLog as suspendCardLog, transactionHistory, updateUserToNewCard, userDetails, validateSendMoney } from '../../utils/constants';
import { MethodType } from '../../utils/method-type';
import { UtilityService } from '../../utils/utility-service';
import { AddCardRequest } from './dto/add.card.request';
import { AddPropCardV2Request } from './dto/add.prop.cardv2.request';
import { AddPropCardV3Request } from './dto/add.prop.cardv3.request';
import { ApplyCardRequest } from './dto/apply.card.request';
import { CancelCardRequest } from './dto/cancel.card.request';
import { CardToCardTransferRequest } from './dto/card.to.card.transfer.request';
import { CardTypeV2Request } from './dto/card.type.v2';
import { ChangeNickNameRequest } from './dto/change.nickname.request';
import { CIPStatusRequest } from './dto/cip.status.request';
import { DeleteCardRequest } from './dto/delete.card.request';
import { ExpressCheckBalanceRequest } from './dto/express.check.balance.request';
import { FavoriteCardRequest } from './dto/favorite.card.request';
import { GeneratePinRequest } from './dto/generate.pin.request';
import { LockUnLockCardRequest } from './dto/lock.unlock.card.request';
import { MoveExpressCheckBalanceToCardRequest } from './dto/move.express.code.to.card.request';
import { OnBoardStatusRequest } from './dto/onboard.status.request';
import { RefreshWallet } from './dto/refresh.wallet.response';
import { RegisterComchekDraftRequest } from './dto/register.comchek.draft.request';
import { ResetPinRequest } from './dto/reset_pin_request';
import { SendMoneyRequest } from './dto/send.money.request';
import { SetOnBoardStatusRequest } from './dto/set.onboard.status.request';
import { TransactionRequest } from './dto/transaction.request';
import { UpdateNewCardRequest } from './dto/update.new.card.request';
import { ValidateMoneyRequest } from './dto/validate.money.request';

@Injectable()
export class CardService {
    constructor(private readonly httpService: MWHttpService,
        private readonly utilityService: UtilityService,) { }

    async checkDrivenUser<T>(headers: HeaderDTO): Promise<T> {
        const url = getUrl(checkDrivenUser);
        const params = {
            deviceId: headers.deviceid,
            versionNumber: headers.versionnumber,
            applicationName: headers.applicationname,
        };
        this.utilityService.logRequest<T>('checkDrivenUser', params, url, headers);
        return await this.httpService.processRequest(url, null, headers, MethodType.GET, params);
    }

    async refreshWallet<T>(headers: HeaderDTO): Promise<RefreshWallet> {
        const request = new AppInfo(headers.deviceid, headers.versionnumber, headers.applicationname);
        const url = getUrl(refreshWallet);
        this.utilityService.logRequest<T>('refreshWallet', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async getTransactionHistory<T>(headers: HeaderDTO, request: TransactionRequest): Promise<T> {
        const url = getUrl(transactionHistory);
        this.utilityService.logRequest<T>('getTransactionHistory', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async addCardToWalletV3<T>(headers: HeaderDTO, request: AddCardRequest): Promise<T> {
        const url = getCardMapiUrl(addCardToWalletV3);
        this.utilityService.logRequest<T>('addCardToWalletV3', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async addPropCardWalletV2<T>(headers: HeaderDTO, request: AddPropCardV2Request): Promise<T> {
        request.cardToken = headers.cardtoken
        const url = getUrl(propCardWalletV2);
        this.utilityService.logRequest<T>('propCardWalletV2', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async addPropCardWalletV3<T>(headers: HeaderDTO, request: AddPropCardV3Request): Promise<T> {
        const url = getUrl(propCardWalletV3);
        this.utilityService.logRequest<T>('propCardWalletV3', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async addPropCardToWalletMapi<T>(headers: HeaderDTO, request: AddPropCardV3Request): Promise<T> {
        const url = getCardMapiUrl(addCardToWalletMapi);
        this.utilityService.logRequest<T>('addCardToWalletMAPI', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async generatePINKey<T>(headers: HeaderDTO, request: GeneratePinRequest): Promise<T> {
        const url = getUrl(generatePINKey);
        this.utilityService.logRequest<T>('generatePINKey', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async applyCard<T>(headers: HeaderDTO, request: ApplyCardRequest): Promise<T> {
        const url = getUrl(applyCard);
        this.utilityService.logRequest<T>('applyCard', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async autoLockUnlock<T>(headers: HeaderDTO, request: any): Promise<T> {
        const url = getUrl(autoLockUnlock);
        this.utilityService.logRequest<T>('autoLockUnlock', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async deleteCard<T>(headers: HeaderDTO, request: DeleteCardRequest): Promise<T> {
        const url = getUrl(cardWallet);
        this.utilityService.logRequest<T>('deleteCard', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.DELETE);
    }

    async deleteCardMapi<T>(headers: HeaderDTO, request: DeleteCardRequest): Promise<T> {
        const url = getCardMapiUrl(deleteCardMapi);
        this.utilityService.logRequest<T>('deleteCardMAPI', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.PUT);
    }

    async updateNickName<T>(headers: HeaderDTO, request: ChangeNickNameRequest): Promise<T> {
        const url = getUrl(cardWallet);
        this.utilityService.logRequest<T>('updateNickName', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.PUT);
    }

    async updateFavorite<T>(headers: HeaderDTO, request: FavoriteCardRequest): Promise<T> {
        const url = getUrl(cardWallet);
        this.utilityService.logRequest<T>('updateFavorite', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.PATCH);
    }

    async updateUserToNewCard<T>(headers: HeaderDTO, request: UpdateNewCardRequest): Promise<T> {
        const url = getUrl(updateUserToNewCard);
        this.utilityService.logRequest<T>('updateUserToNewCard', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async getExpressCheckBalance<T>(headers: HeaderDTO, request: ExpressCheckBalanceRequest): Promise<T> {
        const url = getUrl(getExpressCheckBalance);
        this.utilityService.logRequest<T>('getExpressCheckBalance', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async moveExpressCheckBalanceToCard<T>(headers: HeaderDTO, request: MoveExpressCheckBalanceToCardRequest): Promise<T> {
        const url = getUrl(moveExpressCheckBalanceToCard);
        this.utilityService.logRequest<T>('moveExpressCheckBalanceToCard', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async validateSendMoney<T>(headers: HeaderDTO, request: ValidateMoneyRequest): Promise<T> {
        const req = {
            fromUserId: headers.username,
            toUserId: request.toUserId
        };
        const url = getUrl(validateSendMoney);
        this.utilityService.logRequest<T>('validateSendMoney', req, url, headers);
        return await this.httpService.processRequest(url, req, headers, MethodType.POST);
    }

    async cardToCardTransfer<T>(headers: HeaderDTO, request: CardToCardTransferRequest): Promise<T> {
        const url = getUrl(cardToCardTransfer);
        this.utilityService.logRequest<T>('cardToCardTransfer', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async getCipStatus<T>(headers: HeaderDTO, request: CIPStatusRequest): Promise<T> {
        const url = getUrl(cipStatus);
        this.utilityService.logRequest<T>('getCipStatus', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async sendMoney<T>(headers: HeaderDTO, request: SendMoneyRequest): Promise<T> {
        const url = getUrl(sendMoney);
        this.utilityService.logRequest<T>('sendMoney', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async registerComchekDraft<T>(headers: HeaderDTO, request: RegisterComchekDraftRequest): Promise<T> {
        const url = getUrl(registerComchekDraft);
        this.utilityService.logRequest<T>('registerComchekDraft', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }


    async resetPin<T>(headers: HeaderDTO, request: ResetPinRequest): Promise<T> {
        const url = getUrl(resetPin);
        this.utilityService.logRequest<T>('resetPin', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async cardTypeV2<T>(headers: HeaderDTO, request: CardTypeV2Request): Promise<T> {
        const url = getUrl(cardTypeV2);
        this.utilityService.logRequest<T>('cardTypeV2', request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async cardholderOnboardStatus<T>(headers: HeaderDTO, request: OnBoardStatusRequest) {
        this.utilityService.logRequest<T>('cardholderOnboardStatusCounts', request);
        const url = getCardMapiUrl(onboardStatus);
        const status = request.status;
        interface StatusResponse {
            response: any[];
        }
        const onboardStatusRequest = this.httpService.processRequest<StatusResponse>(url, null, headers, MethodType.GET, request)
            .catch(error => {
                this.utilityService.logError(`Error fetching requests for status: ${status}`, error);
                return { response: [] };
            });
        const [onboardStatusResponse] = await Promise.all([onboardStatusRequest]);
        return {
            requests: onboardStatusResponse?.response,
        };
    }

    async getUserDetails<T>(headers: HeaderDTO, sendMoneyRequest: SendMoneyRequest): Promise<any> {
        const url = `${process.env.LOGIN_VANITY}${userDetails}`;
        const request = {
            "userName": sendMoneyRequest.toUserId,
        }
        this.utilityService.logRequest(getUserDetails, request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.GET);
    }

    async setCardholderOnboardStatus<T>(headers: HeaderDTO, request: SetOnBoardStatusRequest): Promise<any> {
        const url = getCardMapiUrl(setOnboardStatus);
        this.utilityService.logRequest(setOnboardStatusLog, request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async suspendCard<T>(headers: HeaderDTO, request: CancelCardRequest): Promise<any> {
        const url = getCardMapiUrl(statusRemoveCardMapi);
        this.utilityService.logRequest(suspendCardLog, request, url, headers);
        return await this.httpService.processRequest(url, request, headers, MethodType.PUT);
    }

}
