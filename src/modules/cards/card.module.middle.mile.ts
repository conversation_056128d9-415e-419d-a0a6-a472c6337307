import { Module } from '@nestjs/common';

import { AppLogger } from '../../core/logger/logger.service';
import { MWHttpService } from '../../core/service/http.service';
import { UtilityService } from '../../utils/utility-service';
import { AuthService } from '../auth/auth.service';
import { CardControllerMiddleMile } from './card.controller.middle.mile';
import { CardServiceMiddleMile } from './card.service.middle.mile';

@Module({
    controllers: [CardControllerMiddleMile],
    providers: [CardServiceMiddleMile, MWHttpService, AppLogger, AuthService, UtilityService],
})
export class CardModuleMiddleMile { }
