import { Body, Controller, Delete, Get, Headers, Post, Put, UseGuards, UseInterceptors, UsePipes, ValidationPipe } from '@nestjs/common';

import { HeaderDTO } from '../../core/dto/header_dto';
import { ValidHeadersGuard } from '../../core/guards/valid.headers.guard';
import { LoggingInterceptor } from '../../core/interceptors/logging.interceptor';
import { TimeoutInterceptor } from '../../core/interceptors/timeout.interceptor';
import { successCode, successCode2, successResponse } from '../../utils/constants';
import { CardService } from './card.service';
import { CardServiceMiddleMile } from './card.service.middle.mile';
import { AddCardFuelSessionRequest } from './dto/add.card.fuel.session.request';
import { AddCardRequest } from './dto/add.card.request';
import { AddPropCardV2Request } from './dto/add.prop.cardv2.request';
import { AddPropCardV3Request } from './dto/add.prop.cardv3.request';
import { ApplyCardRequest } from './dto/apply.card.request';
import { CancelCardRequest } from './dto/cancel.card.request';
import { CardToCardTransferRequest } from './dto/card.to.card.transfer.request';
import { CardTypeV2Request } from './dto/card.type.v2';
import { ChangeNickNameRequest } from './dto/change.nickname.request';
import { CIPStatusRequest } from './dto/cip.status.request';
import { DeleteCardRequest } from './dto/delete.card.request';
import { ExpressCheckBalanceRequest } from './dto/express.check.balance.request';
import { FavoriteCardRequest } from './dto/favorite.card.request';
import { GeneratePinRequest } from './dto/generate.pin.request';
import { LockUnLockCardRequest } from './dto/lock.unlock.card.request';
import { MoveExpressCheckBalanceToCardRequest } from './dto/move.express.code.to.card.request';
import { OnBoardStatusRequest } from './dto/onboard.status.request';
import { RegisterComchekDraftRequest } from './dto/register.comchek.draft.request';
import { ResetPinRequest } from './dto/reset_pin_request';
import { SendMoneyRequest } from './dto/send.money.request';
import { SetOnBoardStatusRequest } from './dto/set.onboard.status.request';
import { TransactionRequest } from './dto/transaction.request';
import { UpdateNewCardRequest } from './dto/update.new.card.request';
import { ValidateMoneyRequest } from './dto/validate.money.request';

@Controller({ version: '1', path: 'cards' })
@UseGuards(ValidHeadersGuard)
@UseInterceptors(TimeoutInterceptor, LoggingInterceptor)
@UsePipes(new ValidationPipe({ transform: true }))

export class CardController {

    constructor(private readonly cardService: CardService, private readonly cardServiceMiddleMile: CardServiceMiddleMile) { }

    @Get('checkDrivenUser')
    async checkDrivenUser(@Headers() headers) {
        return await this.cardService.checkDrivenUser(headers);
    }

    @Get('refreshWalletV2')
    async refreshWallet(@Headers() headers) {
        return await this.cardService.refreshWallet(headers);
    }

    @Post('transactionHistory')
    async transactionHistory(@Headers() headers: HeaderDTO, @Body() request: TransactionRequest) {
        return await this.cardService.getTransactionHistory(headers, request);
    }

    @Post('addCardToWalletV3')
    async addCardToWalletV3(@Headers() headers: HeaderDTO, @Body() request: AddCardRequest) {
        return await this.cardService.addCardToWalletV3(headers, request);
    }

    @Post('addPropCardWalletV2')
    async propCardWalletV2(@Headers() headers: HeaderDTO, @Body() request: AddPropCardV2Request) {
        return await this.cardService.addPropCardWalletV2(headers, request);
    }

    @Post('addPropCardWalletV03')
    async addPropCardWalletV03(@Headers() headers: HeaderDTO, @Body() request: AddPropCardV3Request) {
        const response = await this.cardService.addPropCardWalletV3(headers, request);
        return response;
    }

    @Post('addCardWalletMapi')
    async addCardWalletMapi(@Headers() headers: HeaderDTO, @Body() request: AddPropCardV3Request) {
        const response = await this.cardService.addPropCardToWalletMapi(headers, request);
        return response;
    }

    @Post('generatePINKey')
    async generatePINKey(@Headers() headers: HeaderDTO, @Body() request: GeneratePinRequest) {
        return await this.cardService.generatePINKey(headers, request);
    }

    @Post('applyCard')
    async applyCard(@Headers() headers: HeaderDTO, @Body() request: ApplyCardRequest) {
        return await this.cardService.applyCard(headers, request);
    }

    @Post('autoLockUnlock')
    async autoLockUnlock(@Headers() headers: HeaderDTO, @Body() request: LockUnLockCardRequest) {
        const { nickName, autoLockCardLockStatus } = request;
        const response = await this.cardService.autoLockUnlock(headers, { nickName, autoLockCardLockStatus });

        if (response['responseCode'] === successCode) {
            const addCardFuelSessionRequest: AddCardFuelSessionRequest = {
                mobileDeviceToken: request.mobileDeviceToken,
                cardToken: request.cardToken,
                fuelSessionType: request.fuelSessionType,
                sessionEnd: request.sessionEnd,
                sessionStart: request.sessionStart,
                sysAccountId: request.sysAccountId,
                userId: request.userId,
            };
            try {

                this.cardServiceMiddleMile.fuelSession(headers, addCardFuelSessionRequest);
            } catch (error) {
                console.warn(
                    `Error addCardFuelSessionRequest ${addCardFuelSessionRequest.cardToken}: ${error.message}`,
                    { cardToken: addCardFuelSessionRequest.cardToken, error }
                );
            }

        }

        return response;
    }

    @Put('updateFavorite')
    async updateFavorite(@Headers() headers: HeaderDTO, @Body() request: FavoriteCardRequest) {
        return await this.cardService.updateFavorite(headers, request);
    }

    @Put('updateNickName')
    async updateNickName(@Headers() headers: HeaderDTO, @Body() request: ChangeNickNameRequest) {
        return await this.cardService.updateNickName(headers, request);
    }

    @Delete('deleteCard')
    async deleteCard(@Headers() headers: HeaderDTO, @Body() request: DeleteCardRequest) {
        const last4Letters = request.cardToken.slice(-4);
        const first4Letters = request.lastName.slice(0, 4);
        request.nickName = `${first4Letters ?? ''}${last4Letters}`.toUpperCase();;
        const response = await this.cardService.deleteCard(headers, request);
        try {
            if (response["responseCode"] === successCode) {
                const cardMapiResponse = await this.cardService.deleteCardMapi(headers, request);
                if (cardMapiResponse["responseCode"] === successCode2) {
                    response["deleteCardMapiResponse"] = successResponse
                }
            }
        }
        catch (error) {
            console.error("deleteCardMapi An error occurred:", error);
        }
        return response;
    }

    @Post('updateUserToNewCard')
    async updateUserToNewCard(@Headers() headers: HeaderDTO, @Body() request: UpdateNewCardRequest) {
        return await this.cardService.updateUserToNewCard(headers, request);
    }

    @Post('getExpressCheckBalance')
    async getExpressCheckBalance(@Headers() headers: HeaderDTO, @Body() request: ExpressCheckBalanceRequest) {
        return await this.cardService.getExpressCheckBalance(headers, request);
    }

    @Post('moveExpressCheckBalanceToCard')
    async moveExpressCheckBalanceToCard(@Headers() headers: HeaderDTO, @Body() request: MoveExpressCheckBalanceToCardRequest) {
        return await this.cardService.moveExpressCheckBalanceToCard(headers, request);
    }

    @Post('validateSendMoney')
    async validateSendMoney(@Headers() headers: HeaderDTO, @Body() request: ValidateMoneyRequest) {
        return await this.cardService.validateSendMoney(headers, request);
    }

    @Post('sendMoney')
    async sendMoney(@Headers() headers: HeaderDTO, @Body() request: SendMoneyRequest) {
        return await this.cardService.sendMoney(headers, request);
    }

    @Post('peerToPeer')
    async peerToPeer(@Headers() headers: HeaderDTO, @Body() request: SendMoneyRequest) {
        // Get user details from the Login API
        const userDetails = await this.cardService.getUserDetails(headers, request);
        let validateResponse = null;
        if (userDetails?.mainframeUserId !== null) {
            request.toUserId = userDetails.mainframeUserId;
            validateResponse = await this.cardService.validateSendMoney(headers, request);
        }
        return {
            validateResponse: validateResponse,
            username: userDetails?.firstName + ' ' + userDetails?.lastName,
            userId: userDetails?.mainframeUserId,
        }
    }

    @Post('registerComchekDraft')
    async registerComchekDraft(@Headers() headers: HeaderDTO, @Body() request: RegisterComchekDraftRequest) {
        return await this.cardService.registerComchekDraft(headers, request);
    }

    @Post('resetPin')
    async resetPin(@Headers() headers: HeaderDTO, @Body() request: ResetPinRequest) {
        return await this.cardService.resetPin(headers, request);
    }

    @Post('cardTypeV2')
    async cardTypeV2(@Headers() headers: HeaderDTO, @Body() request: CardTypeV2Request) {
        return await this.cardService.cardTypeV2(headers, request);
    }

    @Post('onboardStatus')
    async getCardholderOnboardStatus(@Headers() headers: HeaderDTO, @Body() request: OnBoardStatusRequest) {
        return await this.cardService.cardholderOnboardStatus(headers, request);
    }

    @Put('onboardStatus')
    async setCardholderOnboardStatus(@Headers() headers: HeaderDTO, @Body() request: SetOnBoardStatusRequest) {
        return await this.cardService.setCardholderOnboardStatus(headers, request);
    }

    @Put('suspendCard')
    async suspendCard(@Headers() headers: HeaderDTO, @Body() request: CancelCardRequest) {
        return await this.cardService.suspendCard(headers, request);
    }

    @Post('cardToCardTransfer')
    async cardToCardTransfer(@Headers() headers: HeaderDTO, @Body() request: CardToCardTransferRequest) {
        return await this.cardService.cardToCardTransfer(headers, request);
    }

    @Post('cipStatus')
    async getCipStatus(@Headers() headers: HeaderDTO, @Body() request: CIPStatusRequest) {
        return await this.cardService.getCipStatus(headers, request);
    }

}
