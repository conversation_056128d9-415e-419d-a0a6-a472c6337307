import { Body, Controller, Headers, Post, UseGuards, UseInterceptors, UsePipes, ValidationPipe } from '@nestjs/common';

import { HeaderDTO } from '../../core/dto/header_dto';
import { LoggingInterceptor } from '../../core/interceptors/logging.interceptor';
import { TimeoutInterceptor } from '../../core/interceptors/timeout.interceptor';
import { AddCardFuelSessionRequest } from './dto/add.card.fuel.session.request';
import { AddCardStatusRequest } from './dto/add.card.status.request';
import { RemoveCardStatusRequest } from './dto/remove.card.status.request';
import { CardServiceMiddleMile } from './card.service.middle.mile';
import { ValidHeadersMiddleMileGuard } from '../../core/guards/valid.headers.middle.mile.guard';

@Controller({ version: '1', path: 'cards' })
@UseGuards(ValidHeadersMiddleMileGuard)
@UseInterceptors(TimeoutInterceptor, LoggingInterceptor)
@UsePipes(new ValidationPipe({ transform: true }))

export class CardControllerMiddleMile {

    constructor(private readonly cardServiceMiddleMile: CardServiceMiddleMile) { }

    @Post('fuelSession')
    async fuelSession(@Headers() headers: HeaderDTO, @Body() request: AddCardFuelSessionRequest) {
        return await this.cardServiceMiddleMile.fuelSession(headers, request);
    }

    @Post('status/addCard')
    async statusAddCard(@Headers() headers: HeaderDTO, @Body() request: AddCardStatusRequest) {
        return await this.cardServiceMiddleMile.statusAddCard(headers, request);
    }

    @Post('status/removeCard')
    async statusRemoveCard(@Headers() headers: HeaderDTO, @Body() request: RemoveCardStatusRequest) {
        return await this.cardServiceMiddleMile.statusRemoveCard(headers, request);
    }

}
