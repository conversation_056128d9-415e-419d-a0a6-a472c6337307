import { Module } from '@nestjs/common';

import { AppLogger } from '../../core/logger/logger.service';
import { MWHttpService } from '../../core/service/http.service';
import { UtilityService } from '../../utils/utility-service';
import { AuthService } from '../auth/auth.service';
import { CardController } from './card.controller';
import { CardService } from './card.service';
import { CardServiceMiddleMile } from './card.service.middle.mile';

@Module({
    controllers: [CardController],
    providers: [CardService, MWHttpService, AppLogger, AuthService, UtilityService, CardServiceMiddleMile],
})
export class CardModule { }
