import { Injectable } from '@nestjs/common';

import { HeaderDTO } from '../../core/dto/header_dto';
import { MWHttpService } from '../../core/service/http.service';
import { getCardMapiUrl } from '../../utils/api-routes';
import { fuelSessionMapi, statusAddCardMapi, statusRemoveCardMapi } from '../../utils/constants';
import { MethodType } from '../../utils/method-type';
import { UtilityService } from '../../utils/utility-service';
import { AddCardFuelSessionRequest } from './dto/add.card.fuel.session.request';
import { AddCardStatusRequest } from './dto/add.card.status.request';
import { RemoveCardStatusRequest } from './dto/remove.card.status.request';

@Injectable()
export class CardServiceMiddleMile {
    constructor(private readonly httpService: MWHttpService,
        private readonly utilityService: UtilityService,) { }

    async fuelSession<T>(headers: HeaderDTO, request: AddCardFuelSessionRequest): Promise<T> {
        this.utilityService.logRequest<T>('fuelSession', request);
        const url = getCardMapiUrl(fuelSessionMapi);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async statusAddCard<T>(headers: HeaderDTO, request: AddCardStatusRequest): Promise<T> {
        this.utilityService.logRequest<T>('statusAddCard', request);
        const url = getCardMapiUrl(statusAddCardMapi);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async statusRemoveCard<T>(headers: HeaderDTO, request: RemoveCardStatusRequest): Promise<T> {
        this.utilityService.logRequest<T>('statusRemoveCard', request);
        const url = getCardMapiUrl(statusRemoveCardMapi);
        return await this.httpService.processRequest(url, request, headers, MethodType.PUT);
    }
}
