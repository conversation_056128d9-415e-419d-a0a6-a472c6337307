import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";

import { AppInfo } from "../../../utils/app.info";

export class ResetPinRequest extends AppInfo {
    @ApiProperty()
    @IsNotEmpty()
    expiryDate: string

    @ApiProperty()
    @IsNotEmpty()
    securityCode: string

    @ApiProperty()
    @IsNotEmpty()
    taxId: string

    @ApiProperty()
    @IsNotEmpty()
    newPin: string

    @ApiProperty()
    @IsNotEmpty()
    cardTokenNumber: string
}