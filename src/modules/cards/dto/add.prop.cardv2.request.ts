import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

import { AppInfo } from '../../../utils/app.info';

export class AddPropCardV2Request extends AppInfo {
    @ApiProperty()
    @IsNotEmpty()
    nickname: string;

    @ApiProperty()
    @IsNotEmpty()
    cardToken: string;

    @ApiProperty()
    @IsNotEmpty()
    authCodeFlag: string;

    @ApiProperty()
    @IsNotEmpty()
    authenticationCode: string;

    @ApiProperty()
    @IsNotEmpty()
    favoriteCard: string;

    @ApiProperty()
    @IsNotEmpty()
    pinNumber: string;

    @ApiProperty()
    pinEnteredFlag: string;
}



