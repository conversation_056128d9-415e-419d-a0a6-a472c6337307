import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class SetOnBoardStatusRequest {
    @ApiProperty()
    @IsNotEmpty()
    onBoardingId: string;

    @ApiProperty()
    @IsNotEmpty()
    userId: string;

    @ApiProperty()
    @IsNotEmpty()
    fleetId: string;

    @IsNotEmpty()
    @ApiProperty()
    status: string;

    @ApiProperty()
    @IsNotEmpty()
    sysAccountId: string;

    @ApiProperty()
    @IsNotEmpty()
    cardToken: string;

    @ApiProperty()
    @IsNotEmpty()
    mobileDeviceToken: string;

}