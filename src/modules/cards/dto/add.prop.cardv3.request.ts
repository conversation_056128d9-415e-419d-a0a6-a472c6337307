import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

import { AppInfo } from '../../../utils/app.info';

export class AddPropCardV3Request extends AppInfo {
    @ApiProperty()
    @IsNotEmpty()
    cardToken: string;

    @ApiProperty()
    authenticationCode: string;

    @ApiProperty()
    authCodeFlag: string;

    @ApiProperty()
    nickname: string;

    @ApiProperty()
    favoriteCardFlg: string;

    @ApiProperty()
    pinEnteredFlag: string;

    @ApiProperty()
    pinNumber: string;

    @ApiProperty()
    firstName: string;

    @ApiProperty()
    lastName: string;

    @ApiProperty()
    phoneNumber: string;

    @ApiProperty()
    email: string;

    @ApiProperty()
    sysAccountId: string;

    @ApiProperty()
    fleetId: string;

    @ApiProperty()
    userId: string;

    @ApiProperty()
    skipValidation: string;
}



