import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional } from "class-validator";

import { AppInfo } from "../../../utils/app.info";

export class MoveExpressCheckBalanceToCardRequest extends AppInfo {
    @ApiProperty()
    @IsNotEmpty()
    expressCheckNumber: string;

    @ApiProperty()
    @IsNotEmpty()
    expressCheckAmount: string;

    @ApiProperty()
    @IsOptional()
    tripNumber: string;

    @ApiProperty()
    @IsOptional()
    driverNumber: string;

    @ApiProperty()
    @IsOptional()
    unitNumber: string;

    @ApiProperty()
    @IsOptional()
    payeeName: string;
}