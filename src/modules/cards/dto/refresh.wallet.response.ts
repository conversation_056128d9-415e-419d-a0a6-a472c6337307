export class RefreshWallet {
    requestUID: string;
    responseCode: string;
    responseMessage: string;
    cards: JAPICard[];
}

export class JAPICard {
    nickName: string;
    cardNumber: string;
    cardType: string;
    cardStatus: string;
    cardBalance: string;
    favoriteCard: string;
    primaryCard: string;
    accountCode: string;
    custID: string;
    altFundingFlag: string;
    cardToken: string;
    cardProdType: string;
    autoLockCustEnrollStatus: string;
    eicConsentFlg: string;
    corpCode: string;
    corpName: string;
    autoLockCustUnLockDuration: string;
    autoLockLastUpdateDate: Date;
    autoLockLastUpdateTime: string;
    remainingUnlockTime: string;
    autoLockCardLockStatus: string;
}