import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class AddCardFuelSessionRequest {
    @ApiProperty()
    @IsNotEmpty()
    mobileDeviceToken: string;

    @ApiProperty()
    @IsNotEmpty()
    cardToken: string;

    @ApiProperty()
    @IsNotEmpty()
    fuelSessionType: string;

    @ApiProperty()
    @IsNotEmpty()
    userId: string;

    @ApiProperty()
    @IsNotEmpty()
    sessionStart: string;

    @ApiProperty()
    @IsNotEmpty()
    sessionEnd: string;

    @ApiProperty()
    sysAccountId: string;
}