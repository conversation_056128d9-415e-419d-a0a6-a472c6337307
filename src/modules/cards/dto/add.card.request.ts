import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

import { AppInfo } from '../../../utils/app.info';

export class AddCardRequest extends AppInfo {
    @ApiProperty()
    @IsNotEmpty()
    nickname: string;

    @ApiProperty()
    @IsNotEmpty()
    cardToken: string;

    @ApiProperty()
    @IsNotEmpty()
    expDate: string;

    @ApiProperty()
    @IsNotEmpty()
    secCode: string;

    @ApiProperty()
    @IsNotEmpty()
    favoriteCard: string;

    @ApiProperty()
    @IsNotEmpty()
    employeeId: string;

    @ApiProperty()
    payrollSettlement: string;

    @ApiProperty()
    skipValidation: string;
}



