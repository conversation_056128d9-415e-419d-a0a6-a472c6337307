import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";

import { AppInfo } from "../../../utils/app.info";

export class LockUnLockCardRequest extends AppInfo {
    @ApiProperty()
    @IsNotEmpty()
    nickName: string;

    @ApiProperty()
    @IsNotEmpty()
    autoLockCardLockStatus: string;

    @ApiProperty()
    mobileDeviceToken: string;

    @ApiProperty()
    cardToken: string;

    @ApiProperty()
    fuelSessionType: string;

    @ApiProperty()
    userId: string;

    @ApiProperty()
    sessionStart: string;

    @ApiProperty()
    sessionEnd: string;

    @ApiProperty()
    sysAccountId: string;
}