import { Injectable } from '@nestjs/common';

import { HeaderDTO } from '../../core/dto/header_dto';
import { MWHttpService } from '../../core/service/http.service';
import { getUrl } from '../../utils/api-routes';
import { alertSetting } from '../../utils/constants';
import { MethodType } from '../../utils/method-type';
import { UtilityService } from '../../utils/utility-service';
import { FraudAlertRequest } from './dto/fraud.alert.request';
import { UpdateFraudAlertRequest } from './dto/update.fraud.alert.request';

@Injectable()
export class AlertsService {
    constructor(
        private readonly httpService: MWHttpService,
        private readonly utilityService: UtilityService,
    ) { }

    async alertSetting<T>(headers: HeaderDTO, request: FraudAlertRequest): Promise<T> {
        this.utilityService.logRequest<T>('alertSetting', request);
        const url = getUrl(alertSetting);
        const params = {
            nickname: request.nickname,
            deviceId: headers.deviceid,
            versionNumber: headers.versionnumber,
            applicationName: headers.applicationname,
        }
        return await this.httpService.processRequest(url, null, headers, MethodType.GET, params);
    }

    async updateAlertSetting<T>(headers: HeaderDTO, request: UpdateFraudAlertRequest): Promise<T> {
        this.utilityService.logRequest<T>('updateAlertSetting', request);
        const url = getUrl(alertSetting);
        return await this.httpService.processRequest(url, request, headers, MethodType.PATCH, null);
    }
}
