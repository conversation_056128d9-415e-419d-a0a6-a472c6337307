import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";

import { AppInfo } from "../../../utils/app.info";

export class UpdateFraudAlertRequest extends AppInfo {
    @ApiProperty()
    @IsNotEmpty()
    nickname: string;

    @ApiProperty()
    @IsNotEmpty()
    mobileAlertsFlag: string;

    @ApiProperty()
    @IsNotEmpty()
    phoneNumber: string;

    @ApiProperty()
    @IsNotEmpty()
    emailNotificationFlag: string;

    @ApiProperty()
    emailAddress: string;
}
