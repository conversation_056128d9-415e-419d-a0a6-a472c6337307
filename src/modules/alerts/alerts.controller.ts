import { Body, Controller, Headers, Patch, Post, UseGuards, UseInterceptors, UsePipes, ValidationPipe } from '@nestjs/common';

import { HeaderDTO } from '../../core/dto/header_dto';
import { ValidHeadersGuard } from '../../core/guards/valid.headers.guard';
import { LoggingInterceptor } from '../../core/interceptors/logging.interceptor';
import { TimeoutInterceptor } from '../../core/interceptors/timeout.interceptor';
import { AlertsService } from './alerts.service';
import { UpdateFraudAlertRequest } from './dto/update.fraud.alert.request';

@Controller({ version: '1', path: 'alerts' })
@UseGuards(ValidHeadersGuard)
@UseInterceptors(TimeoutInterceptor, LoggingInterceptor)
@UsePipes(new ValidationPipe({ transform: true }))
export class AlertsController {
    constructor(private readonly alertsService: AlertsService) { }

    @Post('alertSetting')
    async alertSetting(@Headers() headers: HeaderDTO, @Body() request: any) {
        return await this.alertsService.alertSetting(headers, request);
    }

    @Patch('updateAlertSetting')
    async updateAlertSetting(@Headers() headers: HeaderDTO, @Body() request: UpdateFraudAlertRequest) {
        return await this.alertsService.updateAlertSetting(headers, request);
    }
}
