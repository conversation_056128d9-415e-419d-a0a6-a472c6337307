import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";

import { AppInfo } from "../../../utils/app.info";

export class CipCheckV2tRequest extends AppInfo {
    @ApiProperty()
    firstName: string;

    @ApiProperty()
    lastName: string;

    @ApiProperty()
    dateOfBirth: string;

    @ApiProperty()
    zip: string;

    @ApiProperty()
    country: string;

    @ApiProperty()
    streetAddress1: string;

    @ApiProperty()
    streetAddress2: string;

    @ApiProperty()
    city: string;

    @ApiProperty()
    state: string;

    @ApiProperty()
    businessCategory: string;

    @ApiProperty()
    taxIdType: string;

    @ApiProperty()
    applicantJobTitle: string;

    @ApiProperty()
    mcNumber: string;

    @ApiProperty()
    usDotNumber: string;

    @ApiProperty()
    applicantType: string;

    @ApiProperty()
    businessName: string;

    @ApiProperty()
    ein: string;

    @ApiProperty()
    beneficialOwnerV2: BeneficialOwnerV2[];
}

export class BeneficialOwnerV2 {
    @ApiProperty()
    firstName: string;

    @ApiProperty()
    lastName: string;

    @ApiProperty()
    jobTitle: string;

    @ApiProperty()
    country: string;

    @ApiProperty()
    streetAddress1: string;

    @ApiProperty()
    streetAddress2: string;

    @ApiProperty()
    city: string;

    @ApiProperty()
    state: string;

    @ApiProperty()
    zipCode: string;

    @ApiProperty()
    dateOfBirth: string;

    @ApiProperty()
    usCitizen: string;

    @ApiProperty()
    identificationValue: string;

    @ApiProperty()
    identificationType: string;

    @ApiProperty()
    countryOfIssuance: string;
}