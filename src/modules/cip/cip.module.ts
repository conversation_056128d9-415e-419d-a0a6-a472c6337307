import { Module } from '@nestjs/common';

import { AppLogger } from '../../core/logger/logger.service';
import { MWHttpService } from '../../core/service/http.service';
import { UtilityService } from '../../utils/utility-service';
import { AuthService } from '../auth/auth.service';
import { CipController } from './cip.controller';
import { CipService } from './cip.service';

@Module({
  controllers: [CipController],
  providers: [CipService, MWHttpService, AppLogger, AuthService, UtilityService]
})
export class CipModule { }
