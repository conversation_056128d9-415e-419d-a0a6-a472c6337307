import { Injectable } from '@nestjs/common';

import { MWHttpService } from '../../core/service/http.service';
import { getUrl } from '../../utils/api-routes';
import { cipCheckV02, getCategoryWiseBusinessTypes, getNationalIdentificationTypes } from '../../utils/constants';
import { MethodType } from '../../utils/method-type';
import { UtilityService } from '../../utils/utility-service';
import { CipCheckV2tRequest } from './dto/cip.check.v2.request';
import { HeaderDTO } from '../../core/dto/header_dto';

@Injectable()
export class CipService {
    constructor(
        private readonly httpService: MWHttpService,
        private readonly utilityService: UtilityService,
    ) { }

    async cipCheckV02<T>(headers: HeaderDTO, request: CipCheckV2tRequest): Promise<T> {
        this.utilityService.logRequest<T>('cipCheckV02', request);
        const url = getUrl(cipCheckV02);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async getCategoryWiseBusinessTypes<T>(headers: HeaderDTO): Promise<T> {
        this.utilityService.logRequest<T>('getCategoryWiseBusinessTypes', {});
        const url = getUrl(getCategoryWiseBusinessTypes);
        return await this.httpService.processRequest(url, {}, headers, MethodType.GET);
    }

    async getNationalIdentificationTypes<T>(headers: HeaderDTO): Promise<T> {
        this.utilityService.logRequest<T>('getNationalIdentificationTypes', {});
        const url = getUrl(getNationalIdentificationTypes);
        return await this.httpService.processRequest(url, {}, headers, MethodType.GET);
    }
}
