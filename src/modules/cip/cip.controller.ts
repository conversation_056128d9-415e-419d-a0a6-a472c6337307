import { Body, Controller, Get, Headers, Post, UseGuards, UseInterceptors, UsePipes, ValidationPipe } from '@nestjs/common';

import { ValidHeadersGuard } from '../../core/guards/valid.headers.guard';
import { LoggingInterceptor } from '../../core/interceptors/logging.interceptor';
import { TimeoutInterceptor } from '../../core/interceptors/timeout.interceptor';
import { CipService } from './cip.service';
import { CipCheckV2tRequest } from './dto/cip.check.v2.request';
import { HeaderDTO } from '../../core/dto/header_dto';

@Controller({ version: '1', path: 'cip' })
@UseGuards(ValidHeadersGuard)
@UseInterceptors(TimeoutInterceptor, LoggingInterceptor)
@UsePipes(new ValidationPipe({ transform: true }))
export class CipController {
    constructor(private readonly cipService: CipService) { }

    @Post('cipCheckV02')
    async cipCheckV02(@Headers() headers: HeaderDTO, @Body() request: CipCheckV2tRequest) {
        return await this.cipService.cipCheckV02(headers, request);
    }

    @Get('getCategoryWiseBusinessTypes')
    async getCategoryWiseBusinessTypes(@Headers() headers) {
        return await this.cipService.getCategoryWiseBusinessTypes(headers);
    }

    @Get('getNationalIdentificationTypes')
    async getNationalIdentificationTypes(@Headers() headers) {
        return await this.cipService.getNationalIdentificationTypes(headers);
    }
}
