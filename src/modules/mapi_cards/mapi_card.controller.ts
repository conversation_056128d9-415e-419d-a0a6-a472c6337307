import { Body, Controller, Headers, Post, Res, UseGuards, UseInterceptors, UsePipes, ValidationPipe } from "@nestjs/common";

import { HeaderDTO } from "../../core/dto/header_dto";
import { ValidHeadersGuard } from "../../core/guards/valid.headers.guard";
import { LoggingInterceptor } from "../../core/interceptors/logging.interceptor";
import { TimeoutInterceptor } from "../../core/interceptors/timeout.interceptor";
import { CardService } from "../cards/card.service";
import { FavoriteCardRequest } from "../cards/dto/favorite.card.request";
import { RefreshWallet } from "../cards/dto/refresh.wallet.response";
import { UpdateNewCardRequest } from "../cards/dto/update.new.card.request";
import { MapiUpdateDeviceTokenRequest } from "../mapi_notifications/dto/mapi.update.device.token.request";
import { MapiNotificationService } from "../mapi_notifications/mapi.notification.service";
import { Card, CardsResponse } from "./dto/map.get.cards.response";
import { MapiAddCardRequest } from "./dto/mapi.add.card.request";
import { MapiGetCardsRequest } from "./dto/mapi.get.cards.request";
import { MapiGetCardTypeRequest } from "./dto/mapi.get.cardtype.request";
import { MapiGetTxnHistoryRequest } from "./dto/mapi.get.txn.history.request";
import { MapiDeleteCardRequest } from "./dto/mapi.remove.card.request";
import { MapiValidateAuthCodeRequest } from "./dto/mapi.validate.auth.code.request";
import { MapiValidateCardRequest } from "./dto/mapi.validate.card.request";
import { MapiCardService } from "./mapi_card.service";

@Controller({ version: '1', path: 'mapi' })
@UseGuards(ValidHeadersGuard)
@UseInterceptors(TimeoutInterceptor, LoggingInterceptor)
@UsePipes(new ValidationPipe({ transform: true }))

export class MapiCardController {

  constructor(private readonly cardService: MapiCardService, private readonly japiCardService: CardService, private readonly mapiNotificationService: MapiNotificationService) { }

  @Post('cards/add')
  async addCardToWallet(@Headers() headers: HeaderDTO, @Body() mapiAddCardRequest: MapiAddCardRequest, @Res() res: any) {
    const resp = res.json(await this.cardService.addCardToWallet(headers, mapiAddCardRequest, res));
    if (resp.responseCode = '00000') {
      // Update device token for push notification
      this.updateDeviceToken(headers, mapiAddCardRequest.cardToken, mapiAddCardRequest.userId, mapiAddCardRequest.mobileDeviceToken);
    }
    return resp;
  }

  @Post('cards/remove')
  async removeCardFromWallet(@Headers() headers: HeaderDTO, @Body() mapiDeleteCardRequest: MapiDeleteCardRequest) {
    return await this.cardService.removeCardFromWallet(headers, mapiDeleteCardRequest);
  }

  @Post('cards/validate')
  async validateCard(@Headers() headers: HeaderDTO, @Body() mapiValidateCardRequest: MapiValidateCardRequest) {
    return await this.cardService.validateCard(headers, mapiValidateCardRequest);
  }

  @Post('cards/validateAuthCode')
  async validateAuthCode(@Headers() headers: HeaderDTO, @Body() mapiValidateAuthCodeRequest: MapiValidateAuthCodeRequest) {
    return await this.cardService.validateAuthCode(headers, mapiValidateAuthCodeRequest);
  }

  @Post('cards/getCards')
  async getCards(@Headers() headers: HeaderDTO, @Body() request: MapiGetCardsRequest): Promise<CardsResponse> {

    // Fetch cards from wallet
    const mapiResponse: CardsResponse = await this.cardService.getCardsFromWallet(headers, request);

    // Early return if no cards found
    if (!mapiResponse?.response?.length) {
      return mapiResponse;
    }
    mapiResponse.response.forEach((card) => {
      if (card.status) {
        card.status = card.status.trim();
      }
    });
    // Process cards in parallel for better performance
    await Promise.all([
      // this.processCardStatuses(mapiResponse, headers),
      this.fetchCardTypeAndAccountInfo(mapiResponse, headers),
    ]);

    // Update device token for push notification
    const primaryCard = mapiResponse.response.find(card => card.primaryCard === "Y");
    const firstPrimaryCardToken = primaryCard?.cardToken || null;

    try {
      this.updateDeviceToken(headers, firstPrimaryCardToken, request.userId, request.mobileDeviceToken);
    } catch (error) {
      // Log error but don't fail the request
      console.warn('Error while Getting Updating device token:', JSON.stringify(error));
    }

    return mapiResponse;

  }

  private async updateDeviceToken(headers: HeaderDTO, firstPrimaryCardToken: string, userId: string, mobileDeviceToken: string): Promise<void> {

    const mapiUpdateDeviceTokenRequest: MapiUpdateDeviceTokenRequest = {
      mobileDeviceToken: mobileDeviceToken,
      appName: headers.applicationname,
      cardToken: firstPrimaryCardToken,
      userId: userId
    };
    try {
      if (firstPrimaryCardToken)
        this.mapiNotificationService.updateDeviceToken(headers, mapiUpdateDeviceTokenRequest)
    } catch (error) {
      console.warn(
        `Error updateDeviceToken ${mapiUpdateDeviceTokenRequest.cardToken}: ${error.message}`,
       JSON.stringify(error)
      );
    }
  }
  /**
   * Process the active card and update user if found
   * Only processes if there is exactly one active card
   */
  private async processActiveCard(activeCard: Card, headers: HeaderDTO): Promise<void> {

    if (!activeCard?.cardToken) {
      return;
    }

    // Get last 4 digits of cardToken
    const last4Letters = activeCard.cardToken.slice(-4);
    const first4Letters = activeCard.lastName.slice(0, 4);

    const nickName = `${first4Letters ?? ''}${last4Letters}`.toUpperCase();

    const updateCardRequest: UpdateNewCardRequest = {
      deviceId: headers.deviceid,
      versionNumber: headers.versionnumber,
      applicationName: headers.applicationname,
      nickname: nickName

    };

    try {
      await this.japiCardService.updateUserToNewCard(headers, updateCardRequest);
    } catch (error) {
      // Log error but don't fail the request
      console.warn('Error while Updating Primary Card:', JSON.stringify(error));
    }

    //Update card as favorite
    const updateFavRequest: FavoriteCardRequest = {
      deviceId: headers.deviceid,
      versionNumber: headers.versionnumber,
      applicationName: headers.applicationname,
      newFavorite: nickName
    };
    try {
      await this.japiCardService.updateFavorite(headers, updateFavRequest);
    } catch (error) {
      // Log error but don't fail the request
      console.warn('Error while Updating Favorite Card:', JSON.stringify(error));
    }
  }

  /**
   * Enrich MAPI card data with JAPI wallet data
   */
  private async enrichWithJapiWalletData(mapiResponse: CardsResponse, headers: HeaderDTO): Promise<void> {
    try {
      const japiResponse: RefreshWallet = await this.japiCardService.refreshWallet(headers);

      if (!japiResponse?.cards?.length) {
        return;
      }

      // Create a map for faster lookups
      const japiCardMap = new Map(
        japiResponse.cards.map(card => [card.cardToken, card])
      );

      // Merge JAPI wallet data with MAPI response
      mapiResponse.response = mapiResponse.response.map(mapiCard => {
        const japiCard = japiCardMap.get(mapiCard.cardToken);
        return japiCard ? { ...mapiCard, ...japiCard } : mapiCard;
      });
    } catch (error) {
      // Log error but don't fail the request
      console.warn('Error while Getting JAPI Refresh Wallet:', JSON.stringify(error));
    }
  }

  /**
 * Processes active cards and enriches with wallet data
 */
  private async processCardStatuses(mapiResponse: CardsResponse, headers: HeaderDTO): Promise<void> {
    const activeCards = mapiResponse.response.filter(card => card.status === 'A');

    // Process the single active card if it exists
    let processed = false;
    if (activeCards.length === 1) {
      await this.processActiveCard(activeCards[0], headers);
      processed = true;
    }

    if (!processed) {
      for (const card of activeCards) {
        if (card.authCodeAcceptance === 'Y' || card.authCodeAcceptance == null) {
          await this.processActiveCard(card, headers);
          break;
        }
      }
    }

    // Enrich with wallet data if any active cards exist
    if (activeCards.length > 0) {
      await this.enrichWithJapiWalletData(mapiResponse, headers);
    }
  }

  /**
 * Fetches card type and account info for cards with status P or D
 */
  private async fetchCardTypeAndAccountInfo(mapiResponse: CardsResponse, headers: HeaderDTO): Promise<void> {
    // Shailendra: Removed status D and added A and authCodeAcceptance = N because we are not displaying the cards which are declined and we need when admin share a card
    const cardsNeedingInfo = mapiResponse.response.filter(card =>
      ['A', 'P'].includes(card.status) || card.authCodeAcceptance == 'N'
    );

    if (!cardsNeedingInfo.length) {
      return;
    }

    // Process card type and account name requests in parallel for better performance
    const cardTypePromises = cardsNeedingInfo.flatMap(card => [
      this.fetchCardType(card, headers),
      this.fetchCardAccountName(card, headers)
    ]);
    await Promise.allSettled(cardTypePromises);
  }

  /**
   * Fetches card type for a single card
   */
  private async fetchCardType(card: any, headers: HeaderDTO): Promise<void> {
    try {
      const cardTypeRequest: MapiGetCardTypeRequest = {
        cardToken: card.cardToken,
        deviceId: headers.deviceid,
        versionNumber: headers.versionnumber,
        applicationName: headers.applicationname
      };

      const cardTypeResponse = await this.cardService.getCardType(headers, cardTypeRequest);
      if (cardTypeResponse?.response?.cardType) {
        card.cardType = cardTypeResponse.response.cardType;
      }
    } catch (error) {
      console.warn(
        `Error getting card type for card ${card.cardToken}: ${error.message}`,
        JSON.stringify(error)
      );
    }
  }

  /**
   * Fetches card account name for a single card
   */
  private async fetchCardAccountName(card: any, headers: HeaderDTO): Promise<void> {
    try {
      const accountsResponse = await this.cardService.fetchCardAccounts(card, headers);
      if (accountsResponse && accountsResponse?.accounts) {
        card.accountName = accountsResponse?.accounts[0].billingCompanyName;
        card.resellerId = accountsResponse?.accounts[0].resellerId;
      }
    } catch (error) {
      console.warn(
        `Error getting card account name for card ${card.cardToken}: ${error.message}`,
        JSON.stringify(error)
      );
    }
  }

  @Post('cards/cardType')
  async getCardType(@Headers() headers: HeaderDTO, @Body() mapiGetCardTypeRequest: MapiGetCardTypeRequest) {
    return await this.cardService.getCardType(headers, mapiGetCardTypeRequest);
  }

  @Post('cards/getEVCards')
  async getEVCards(@Headers() headers: HeaderDTO, @Body() request: MapiGetCardsRequest): Promise<CardsResponse> {
    return await this.cardService.getEVCardsFromWallet(headers, request);
  }

  @Post('cards/getTxnHistory')
  async getTxnHistory(@Headers() headers: HeaderDTO, @Body() request: MapiGetTxnHistoryRequest): Promise<any> {
    return await this.cardService.getTxnHistory(headers, request);
  }
}