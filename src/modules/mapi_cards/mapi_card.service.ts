import { BadRequestException, Injectable } from "@nestjs/common";

import { HeaderDTO } from "../../core/dto/header_dto";
import { MWHttpService } from "../../core/service/http.service";
import { getCardMapiUrl, getTranMapiUrl } from "../../utils/api-routes";
import { addCardToWalletLog, addCardToWalletMapi, addCardWithAutoAssignEndpoint, addCardWithAutoAssignLog, adminApprovedCardStatus, cardToken, cardTypeNotAccepted, deleteCardMapi, endDate, errorMessage, expiryDateAndSecurityCodeNullError, getAccounts, getCardsEndpoint, getCardsLog, getCardTypeEndpoint, getCardTypeLog, getEVCardsLog, getTxnHistoryLog, invalidInput, removeCardFromWalletLog, requestorType, response, responseCode, startDate, status, success, successCode2, txnHistoryEndpoint, userId, validateAuthCode, validateAuthCodeLog, validateCardEndpoint, validateCardLog } from "../../utils/constants";
import { MethodType } from "../../utils/method-type";
import { UtilityService } from "../../utils/utility-service";
import { CardsResponse } from "./dto/map.get.cards.response";
import { MapiAddCardRequest } from "./dto/mapi.add.card.request";
import { MapiGetCardsRequest } from "./dto/mapi.get.cards.request";
import { MapiGetCardTypeRequest } from "./dto/mapi.get.cardtype.request";
import { MapiGetTxnHistoryRequest } from "./dto/mapi.get.txn.history.request";
import { MapiDeleteCardRequest } from "./dto/mapi.remove.card.request";
import { MapiValidateAuthCodeRequest } from "./dto/mapi.validate.auth.code.request";
import { MapiValidateCardRequest } from "./dto/mapi.validate.card.request";

@Injectable()
export class MapiCardService {
    constructor(private readonly httpService: MWHttpService,
        private readonly utilityService: UtilityService) { }

    async addCardToWallet(headers: HeaderDTO, request: MapiAddCardRequest, res: any): Promise<any> {
        this.utilityService.logRequest(addCardToWalletLog, request);

        if (request.acceptedCardTypes.length > 0) {
            const isValidCardType = await this.isCardTypeAccepted(headers, request);
            if (!isValidCardType) {
                return this.sendErrorMessage(res, cardTypeNotAccepted);
            }
        }

        if (request.isCardValidationRequired) {
            const isValid = await this.isCardValid(headers, request);
            if (!isValid.success) {
                return this.sendErrorMessage(res, isValid[errorMessage]);
            }
        }

        if (request.canAutoAssign) {
            return this.addCardWithAutoAssign(headers, request);
        }

        return this.addCard(headers, request);
    }

    private async isCardTypeAccepted(headers: HeaderDTO, request: MapiAddCardRequest): Promise<boolean> {
        const cardToken = request.cardToken;
        const cardTypeRequest = {
            cardToken,
            deviceId: headers.deviceid,
            versionNumber: headers.versionnumber,
            applicationName: headers.applicationname,
        };

        const cardTypeResponse = await this.getCardType(headers, cardTypeRequest);
        const cardType = cardTypeResponse?.[responseCode] === successCode2
            ? cardTypeResponse?.[response]?.cardType
            : null;

        return request.acceptedCardTypes.includes(cardType?.toLowerCase());
    }

    private async isCardValid(headers: HeaderDTO, request: MapiAddCardRequest): Promise<{ success: boolean;[key: string]: any }> {
        if (request.expiryDate === undefined || request.expiryDate === null || request.securityCode === undefined || request.securityCode === null) {
            throw new BadRequestException(expiryDateAndSecurityCodeNullError);
        }
        const validateCardResponse = await this.validateCard(headers, request);
        const isCardValid = validateCardResponse?.[responseCode] === success;

        return {
            success: isCardValid,
            ...validateCardResponse,
        };
    }

    private sendErrorMessage(res: any, error: string): any {
        return res.status(400).json({
            errorCode: invalidInput,
            errorSummary: error,
            errorMessage: error,
        });
    }

    async addCard(headers: HeaderDTO, request: MapiAddCardRequest): Promise<any> {
        const url = getCardMapiUrl(addCardToWalletMapi);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async addCardWithAutoAssign(headers: HeaderDTO, request: MapiAddCardRequest): Promise<any> {
        this.utilityService.logRequest(addCardWithAutoAssignLog, request);
        const url = getCardMapiUrl(addCardWithAutoAssignEndpoint);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async removeCardFromWallet(headers: HeaderDTO, request: MapiDeleteCardRequest): Promise<any> {
        this.utilityService.logRequest(removeCardFromWalletLog, request);
        const url = getCardMapiUrl(deleteCardMapi);
        return await this.httpService.processRequest(url, request, headers, MethodType.PUT);
    }

    async validateCard(headers: HeaderDTO, request: MapiValidateCardRequest): Promise<any> {
        this.utilityService.logRequest(validateCardLog, request);
        const url = getCardMapiUrl(validateCardEndpoint);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async validateAuthCode(headers: HeaderDTO, request: MapiValidateAuthCodeRequest): Promise<any> {
        this.utilityService.logRequest(validateAuthCodeLog, request);
        const url = getCardMapiUrl(validateAuthCode);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST);
    }

    async getCardsFromWallet(headers: HeaderDTO, mapiGetCardsRequest: MapiGetCardsRequest): Promise<CardsResponse> {
        this.utilityService.logRequest(getCardsLog, mapiGetCardsRequest);
        const url = getCardMapiUrl(getCardsEndpoint);

        const params = this.getQueryParams(mapiGetCardsRequest);

        return await this.httpService.processRequest(url, undefined, headers, MethodType.GET, params);
    }

    private getQueryParams(mapiGetCardsRequest: MapiGetCardsRequest) {
        const params = new URLSearchParams();
        if (mapiGetCardsRequest.userId) params.append(userId, mapiGetCardsRequest.userId);
        if (mapiGetCardsRequest.requestorType) params.append(requestorType, mapiGetCardsRequest.requestorType);
        return params;
    }

    async getEVCardsFromWallet(headers: HeaderDTO, mapiGetCardsRequest: MapiGetCardsRequest): Promise<any> {
        this.utilityService.logRequest(getEVCardsLog, mapiGetCardsRequest);
        const url = getCardMapiUrl(getCardsEndpoint);

        const params = this.getQueryParams(mapiGetCardsRequest);

        const cardsResponse = await this.httpService.processRequest(url, undefined, headers, MethodType.GET, params);
        try {
            if (mapiGetCardsRequest.isCardTypeRequired && cardsResponse?.[responseCode] === successCode2) {
                const updatedCardsResponse = await Promise.all(cardsResponse?.[response]?.map(async (card: any) => {
                    if (card[status].toLowerCase() === adminApprovedCardStatus) {
                        const cardTypeRequest = {
                            deviceId: headers.deviceid,
                            versionNumber: headers.versionnumber,
                            applicationName: headers.applicationname,
                            cardToken: card?.cardToken,
                        };
                        const cardTypeResponse = await this.getCardType(headers, cardTypeRequest);
                        return {
                            ...card,
                            cardType: cardTypeResponse?.[response]?.cardType.toLowerCase(),
                        }
                    }
                    return { ...card, cardType: '' };
                }));
                cardsResponse[response] = updatedCardsResponse;
                return cardsResponse;
            }
        } catch (_) {
            return cardsResponse;
        }
        return cardsResponse;
    }

    async getCardType(headers: HeaderDTO, mapiGetCardTypeRequest: MapiGetCardTypeRequest): Promise<any> {
        this.utilityService.logRequest(getCardTypeLog, mapiGetCardTypeRequest);
        const url = getCardMapiUrl(getCardTypeEndpoint);

        const params = new URLSearchParams();
        if (mapiGetCardTypeRequest.cardToken) params.append(cardToken, mapiGetCardTypeRequest.cardToken);

        return await this.httpService.processRequest(url, undefined, headers, MethodType.GET, params);
    }

    async fetchCardAccounts(card: any, headers: HeaderDTO): Promise<any> {
        const url = `${process.env.LOGIN_VANITY}${getAccounts}`;
        const request = {
            "sysAccountId": card.sysAccountId,
        }
        this.utilityService.logRequest(getCardTypeLog, request);
        return await this.httpService.processRequest(url, request, headers, MethodType.POST, undefined);
    }

    async getTxnHistory(headers: HeaderDTO, mapiGetTxnHistoryRequest: MapiGetTxnHistoryRequest): Promise<any> {
        this.utilityService.logRequest(getTxnHistoryLog, mapiGetTxnHistoryRequest);
        const url = getTranMapiUrl(txnHistoryEndpoint);

        const params = this.getTxnHistoryQueryParams(mapiGetTxnHistoryRequest);
        return await this.httpService.processRequest(url, undefined, headers, MethodType.GET, params);
    }

    private getTxnHistoryQueryParams(mapiGetTxnHistoryRequest: MapiGetTxnHistoryRequest) {
        const params = new URLSearchParams();
        params.append(cardToken, mapiGetTxnHistoryRequest.cardToken);
        params.append(startDate, mapiGetTxnHistoryRequest.startDate);
        params.append(endDate, mapiGetTxnHistoryRequest.endDate);
        params.append(userId, mapiGetTxnHistoryRequest.userId);
        return params;
    }
}