import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

import { AppInfo } from '../../../utils/app.info';

export class MapiAddCardRequest extends AppInfo {

    @ApiProperty()
    @IsNotEmpty()
    cardToken: string;

    @ApiProperty()
    @IsNotEmpty()
    userId: string;

    @ApiProperty()
    @IsNotEmpty()
    fleetId: string;

    @ApiProperty()
    @IsNotEmpty()
    sysAccountId: string;

    @ApiProperty()
    @IsNotEmpty()
    firstName: string;

    @ApiProperty()
    @IsNotEmpty()
    lastName: string;

    @ApiProperty()
    @IsNotEmpty()
    phoneNumber: string;

    @ApiProperty()
    @IsNotEmpty()
    mobileDeviceToken: string;

    @ApiProperty()
    cardNickName: string;

    @ApiProperty()
    expiryDate: string;

    @ApiProperty()
    securityCode: string;

    @ApiProperty()
    favoriteCard: string;

    @ApiProperty()
    email: string;

    @ApiProperty()
    isCardValidationRequired: boolean = false;

    @ApiProperty()
    canAutoAssign: boolean = false;

    @ApiProperty()
    acceptedCardTypes: string[] = [];

    @ApiProperty()
    lastFourCardNumber: string;

    @ApiProperty()
    appName: string;
}



