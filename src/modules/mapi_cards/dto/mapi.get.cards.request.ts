import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

import { AppInfo } from '../../../utils/app.info';

export class MapiGetCardsRequest extends AppInfo {
    @ApiProperty()
    @IsNotEmpty()
    userId: string;

    @ApiProperty()
    requestorType: string = 'S';

    @ApiProperty()
    isCardTypeRequired: boolean = false;

    @ApiProperty()
    mobileDeviceToken: string;


}

