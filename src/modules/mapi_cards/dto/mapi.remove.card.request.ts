import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

import { AppInfo } from '../../../utils/app.info';

export class MapiDeleteCardRequest extends AppInfo {
    @ApiProperty()
    @IsNotEmpty()
    cardToken: string;

    @ApiProperty()
    @IsNotEmpty()
    userId: string;

    @ApiProperty()
    @IsNotEmpty()
    onBoardingId: string;

    @ApiProperty()
    status: String = "CD";

    @ApiProperty()
    appName: string;
}



