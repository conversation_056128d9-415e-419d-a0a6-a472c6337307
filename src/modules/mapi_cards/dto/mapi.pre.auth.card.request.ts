import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

import { AppInfo } from '../../../utils/app.info';

export class MapiPreAuthCardRequest extends AppInfo {
    @ApiProperty()
    @IsNotEmpty()
    cardToken: string;

    @ApiProperty()
    @IsNotEmpty()
    psLocationId: string;

    @ApiProperty()
    @IsNotEmpty()
    mobileDeviceToken: string;

    @ApiProperty()
    @IsNotEmpty()
    userId: string;
}

