import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

import { AppInfo } from '../../../utils/app.info';

export class MapiValidateAuthCodeRequest extends AppInfo {
    @ApiProperty()
    @IsNotEmpty()
    onBoardingId: string;

    @ApiProperty()
    @IsNotEmpty()
    sysAccountId: string;

    @ApiProperty()
    @IsNotEmpty()
    fleetId: string;

    @ApiProperty()
    @IsNotEmpty()
    cardToken: string;

    @ApiProperty()
    @IsNotEmpty()
    mobileDeviceToken: string;

    @ApiProperty()
    @IsNotEmpty()
    userId: string;

    @ApiProperty()
    @IsNotEmpty()
    authCode: string;
}



