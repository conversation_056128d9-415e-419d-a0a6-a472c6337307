export class CardsResponse {
    responseCode: string;
    responseMessage: string;
    response: Card[];
}

export class Card {
    nickName: string;
    cardNumber: string;
    cardType: string;
    cardStatus: string;
    cardBalance: string;
    favoriteCard: string;
    primaryCard: string;
    accountCode: string;
    custID: string;
    altFundingFlag: string;
    cardToken: string;
    cardProdType: string;
    autoLockCustEnrollStatus: string;
    eicConsentFlg: string;
    corpCode: string;
    corpName: string;
    autoLockCustUnLockDuration: string;
    autoLockLastUpdateDate: Date;
    autoLockLastUpdateTime: string;
    remainingUnlockTime: string;
    autoLockCardLockStatus: string;
    firstName: string;
    lastName: string;
    fleetId: string;
    sysAccountId: string;
    authCode: null;
    authCodeDeliveryTS: null;
    email: string;
    phoneNumber: string;
    requestorId: string;
    requestorType: string;
    requestedDate: Date;
    cardholderOnboardId: number;
    status: string;
    statusBy: null;
    statusTS: null;
    authCodeAcceptance: string;
}