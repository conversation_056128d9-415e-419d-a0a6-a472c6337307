import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

import { AppInfo } from '../../../utils/app.info';

export class MapiGetTxnHistoryRequest extends AppInfo {
    @ApiProperty()
    @IsNotEmpty()
    cardToken: string;

    @ApiProperty()
    @IsNotEmpty()
    startDate: string;

    @ApiProperty()
    @IsNotEmpty()
    endDate: string;

    @ApiProperty()
    @IsNotEmpty()
    userId: string;
}