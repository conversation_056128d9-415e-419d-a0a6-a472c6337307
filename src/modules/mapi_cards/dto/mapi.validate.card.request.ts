import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

import { AppInfo } from '../../../utils/app.info';

export class MapiValidateCardRequest extends AppInfo {
    @ApiProperty()
    @IsNotEmpty()
    cardToken: string;

    @ApiProperty()
    @IsNotEmpty()
    expiryDate: string;

    @ApiProperty()
    @IsNotEmpty()
    securityCode: string;

    @ApiProperty()
    @IsNotEmpty()
    userId: string;

    @ApiProperty()
    @IsNotEmpty()
    fleetId: string;
}



