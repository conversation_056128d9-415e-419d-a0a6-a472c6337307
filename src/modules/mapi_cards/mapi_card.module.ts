import { Modu<PERSON> } from '@nestjs/common';

import { AppLogger } from '../../core/logger/logger.service';
import { MWHttpService } from '../../core/service/http.service';
import { UtilityService } from '../../utils/utility-service';
import { AuthService } from '../auth/auth.service';
import { CardService } from '../cards/card.service';
import { MapiNotificationService } from '../mapi_notifications/mapi.notification.service';
import { MapiCardController } from './mapi_card.controller';
import { MapiCardService } from './mapi_card.service';

@Module({
    controllers: [MapiCardController],
    providers: [MapiCardService, MWHttpService, AppLogger, AuthService, UtilityService, CardService, MapiNotificationService],
})

export class MapiCardModule { }
