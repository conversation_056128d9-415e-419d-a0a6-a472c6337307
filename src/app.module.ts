import { HttpModule } from '@nestjs/axios';
import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_INTERCEPTOR } from '@nestjs/core';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { HttpExceptionFilter } from './core/exceptions/http-exception.filter';
import { TimeoutInterceptor } from './core/interceptors/timeout.interceptor';
import { LoggerModule } from './core/logger/logger.module';
import { AlertsModule } from './modules/alerts/alerts.module';
import { AuthModule } from './modules/auth/auth.module';
import { BankModule } from './modules/bank/bank.module';
import { CardModule } from './modules/cards/card.module';
import { CipModule } from './modules/cip/cip.module';
import { CmInfoModule } from './modules/cminfo/cminfo.module';
import { MapiCardModule } from './modules/mapi_cards/mapi_card.module';
import { PreferenceModule } from './modules/preference/preference.module';
import { UserModule } from './modules/user/user.module';
import { CardModuleMiddleMile } from './modules/cards/card.module.middle.mile';
import { MapiNotificationModule } from './modules/mapi_notifications/mapi.notification.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    AuthModule,
    LoggerModule,
    HttpModule.registerAsync({
      useFactory: () => ({
        timeout: 30000,
        maxRedirects: 5,
      }),
    }),
    CardModule,
    CardModuleMiddleMile,
    BankModule,
    AlertsModule,
    CmInfoModule,
    UserModule,
    CipModule,
    PreferenceModule,
    MapiCardModule,
    MapiNotificationModule
  ],
  controllers: [AppController],
  providers: [AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: HttpExceptionFilter,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TimeoutInterceptor,
    },
    // {
    //   provide: APP_INTERCEPTOR,
    //   useClass: LoggingInterceptor,
    // },
  ],
})
export class AppModule implements NestModule {
  configure(_consumer: MiddlewareConsumer) { }
}
