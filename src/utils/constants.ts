// API endpoints
export const token = '/api/v1/app/token';

// User
export const deleteAccount = '/user/deleteAccount';
export const updateCMUserInfo = '/user/updateCMUserInfo';
export const userDetails = '/v1/users/details';
export const getAccounts = '/v1/users/account/getAccounts';

// Cards
export const checkDrivenUser = '/cards/checkDrivenUser';
export const refreshWallet = '/cards/refreshWalletV2';
export const transactionHistory = '/cards/getTransactionHistory';
export const addCardToWalletV3 = '/cards/cardWalletV3';
export const addCardToWalletMapi = '/api/v1/cardholder/onboard/request';
export const deleteCardMapi = '/api/v1/cardholder/status';
export const fuelSessionMapi = '/api/v1/cardholder/fuel-session';
export const statusAddCardMapi = '/api/v1/cardholder/onboard/status';
export const statusRemoveCardMapi = '/api/v1/cardholder/status';
export const propCardWalletV2 = '/cards/propCardWalletV2';
export const propCardWalletV3 = '/cards/propCardWalletV03';
export const generatePINKey = '/cards/generatePINKey';
export const applyCard = '/cards/applyCard';
export const cardWallet = '/cards/cardWallet';
export const autoLockUnlock = '/cards/autoLockUnlock';
export const updateUserToNewCard = '/cards/updateUserToNewCard';
export const getExpressCheckBalance = '/cards/getExpressCheckBalance';
export const moveExpressCheckBalanceToCard = '/cards/moveExpressCheckBalanceToCard';
export const validateSendMoney = '/cards/validateSendMoney';
export const cardToCardTransfer = '/cards/sendMoneyC2C';
export const cipStatus = '/cards/getCipPayrollInfo';
export const sendMoney = '/cards/sendMoney';
export const registerComchekDraft = '/cards/registerComchekDraft';
export const resetPin = '/cards/resetPINV02';
export const cardTypeV2 = '/cards/cardTypeV2';
export const onboardStatus = '/api/v1/cardholder/onboard';
export const setOnboardStatus = '/api/v1/cardholder/onboard/status';


// Bank Management
export const addBankAccount = '/bankManagement/addBankAccount';
export const getBankAccount = '/bankManagement/getBankAccount';
export const validateBankName = '/bankManagement/validateBankName';
export const transferToBank = '/bankManagement/transferToBank';

// Alerts
export const alertSetting = '/alerts/alertSetting';
export const getCMUserInfo = '/cminfo/getCMUserInfo';

// Cip
export const cipCheckV02 = '/cip/cipCheckV02';
export const getCategoryWiseBusinessTypes = '/cip/getCategoryWiseBusinessTypes';
export const getNationalIdentificationTypes = '/cip/getNationalIdentificationTypes';

// Preferences
export const setCMUserPreferences = '/preferences/setCMUserPreferences'
export const getCMUserPreferences = '/preferences/getCMUserPreferences'

//modern api
export const addCardToWallet = '/api/v1/cards/card-to-wallet';
export const validateCardEndpoint = '/api/v1/cardholder/cards/validate';
export const validateAuthCode = '/api/v1/cardholder/auth-code';
export const getCardsEndpoint = '/api/v1/cardholder/onboard';
export const getCardTypeEndpoint = '/api/v1/cardholder/cardtype';
export const addCardWithAutoAssignEndpoint = '/api/v1/cardholder/cardholder-auto-assign';
export const txnHistoryEndpoint = '/api/v1/cdr-details';
export const updateMobileDeviceTokenEndpoint = '/api/v1/cardholder/mobile-device-token';

//logs
export const addCardToWalletLog = 'mapi-addCardToWallet';
export const removeCardFromWalletLog = 'mapi-removeCardFromWallet';
export const validateCardLog = 'mapi-validateCard';
export const getCardsLog = 'mapi-getCards';
export const getCardTypeLog = 'mapi-getCardType';
export const validateAuthCodeLog = 'mapi-validateAuthCode';
export const addCardWithAutoAssignLog = 'mapi-addCardWithAutoAssign';
export const getEVCardsLog = 'mapi-getEVCards';
export const getUserDetails = 'getUserDetails';
export const setOnboardStatusLog = 'setOnboardStatus';
export const suspendCardRequestLog = 'suspendCard';
export const updateFuelSessionLog = 'mapi-updateFuelSession';
export const updateTokenOnAddCardLog = 'mapi-updateTokenOnAddCard';
export const updateTokenOnRemoveCardLog = 'mapi-updateTokenOnRemoveCard';
export const getTxnHistoryLog = 'mapi-getTxnHistory';
export const getAccountsLog = 'getAccounts';
export const updateMobileDeviceTokenLog = 'mapi-updateMobileDeviceToken';

// Error messages
export const unKnownError = 'Unknown error occurred';

export const successCode = '000000'; //japi
export const successCode2 = '00000'; //mapi
export const success = '0000'; //mapi
export const successResponse = 'SUCCESS';
export const failResponse = 'FAIL';
export const responseCode = 'responseCode';
export const responseMessage = 'responseMessage';
export const errorCode = 'errorCode';
export const errorMessage = 'errorMessage';
export const response = 'response';
export const invalidInput = 'INVALID_INPUT';
export const cardTypeNotAccepted = 'Card type not accepted.';

export const userId = 'userId';
export const requestorType = 'requestorType';
export const cardToken = 'cardToken';
export const startDate = 'startDate';
export const endDate = 'endDate';

export const expiryDateAndSecurityCodeNullError = 'expiryDate and securityCode cannot be null';
export const adminApprovedCardStatus = 'a';
export const status = 'status';
export const chargePass = 'EVCHARGEPASSAPP';