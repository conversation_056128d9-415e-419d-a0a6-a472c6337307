import { Injectable } from '@nestjs/common';

import { AppLogger } from '../core/logger/logger.service';

@Injectable()
export class UtilityService {
    constructor(private readonly appLogger: AppLogger) { }

    logRequest<T>(apiName: string, request: any, url?: any, headers?: any) {
        try {
            const requestObj = {
                APIName: apiName,
                Request: JSON.stringify(request),
                URL: JSON.stringify(url),
                requestTime: Date.now(),
                headers: JSON.stringify(headers),
            }
            this.appLogger.log(requestObj);
        } catch (error) {
            this.appLogger.error(
                `Error in logging request ${apiName} :: ${error.message}`,
            );
        }
    }

    logError(errorMessage: string, error: any) {
        this.appLogger.error(
            `ErrorMessage: ${errorMessage}, Error: ${JSON.stringify(error)}`,
        );
    }
}
