import { BadRequestException, CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Observable } from 'rxjs';

import { isNonEmptyString } from '../../utils/custom-validators';
import { HeaderDTO } from '../dto/header_dto';

@Injectable()
export class ValidHeadersGuard implements CanActivate {
    canActivate(
        context: ExecutionContext,
    ): boolean | Promise<boolean> | Observable<boolean> {
        const request = context.switchToHttp().getRequest();
        const headers = request.headers;

        if (this.isValidHeaders(headers)) {
            return true; // Allow the request to proceed
        } else {
            throw new BadRequestException('Invalid or missing headers.');
        }
    }

    private isValidHeaders(headers: HeaderDTO): boolean {
        return isNonEmptyString(headers.authorization) &&
            isNonEmptyString(headers.userid) &&
            isNonEmptyString(headers.sysaccountid) &&
            isNonEmptyString(headers.deviceid) &&
            isNonEmptyString(headers.versionnumber) &&
            isNonEmptyString(headers.applicationname);
    }
}
