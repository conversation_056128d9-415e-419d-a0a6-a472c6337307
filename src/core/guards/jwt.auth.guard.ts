import { Injectable, CanActivate, ExecutionContext, UnauthorizedException, InternalServerErrorException } from '@nestjs/common';
import { Observable } from 'rxjs';
import { AuthService } from '../../modules/auth/auth.service';
import { AppLogger } from '../logger/logger.service';

@Injectable()
export class JWTAuthGuard implements CanActivate {

    constructor(private readonly authService: AuthService, private readonly appLogger: AppLogger,) { }

    canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
        const request = context.switchToHttp().getRequest();
        const authHeader = request.headers.authorization;
        const bearerToken = authHeader && authHeader.split(' ')[1];
        if (!bearerToken) {
            throw new UnauthorizedException();
        }
        return true;
        // return this.verifyToken(bearerToken);
    }

    async verifyToken(bearerToken: string) {
        try {
            const decodedToken = await this.authService.validateToken(bearerToken);
            if (decodedToken.isExpired() || decodedToken.claims.sub != process.env.OKTA_SUB) {
                throw new UnauthorizedException();
            }

            return true;
        } catch (error) {
            this.appLogger.error("AuthGuard", error);
            throw new InternalServerErrorException(error.message);
        }
    }
}