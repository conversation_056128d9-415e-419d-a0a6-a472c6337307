import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable, tap } from 'rxjs';
import { AppLogger } from '../logger/logger.service';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
    constructor(private readonly logger: AppLogger){}

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const now = Date.now();
        const request = context.switchToHttp().getRequest();
        const method = request.method;
        const url = request.url;
        const versionCode = request.headers['VersionCode'];
        const versionName = request.headers['VersionName'];
        const deviceUUID = request.headers['DeviceId'];
        const flavor = request.headers['Flavor'];


        this.logger.log(`Request started: ${url} ${versionName} ${versionCode} ${deviceUUID}`);

        return next.handle().pipe(
            tap(() => {
                const response = context.switchToHttp().getResponse();
                const status = response.statusCode;
                const duration = Date.now() - now;

                this.logger.log(`Request finished: ${method} ${url} - ${status} (duration: ${duration}ms)`);
            }),
        );
    }
}