import { Injectable } from '@nestjs/common';
import axios, { AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

import { unKnownError } from '../../utils/constants';
import { generateRandomChars } from '../../utils/utility';
import { HeaderDTO } from '../dto/header_dto';
import { DrivenError } from '../exceptions/custom-error';
import { AppLogger } from '../logger/logger.service';
import { IHttpClient } from './interface/http.interface';

const requestInterceptor = (config: InternalAxiosRequestConfig) => {
  const requestId = `${generateRandomChars(8)}-BFF-${generateRandomChars(4)}-${generateRandomChars(8)}-${generateRandomChars(12)}`;
  config.headers['request-startTime'] = Date.now();
  console.log(`Modern Request: ${config.url} : X-Request-ID: ${requestId}`);
  config.headers['x-request-id'] = requestId;
  return config;
};

const responseInterceptor = (response: AxiosResponse) => {
  const startTime = response.config.headers['request-startTime'];
  const duration = Date.now() - startTime;
  console.log(`Modern Response: ${response.config.url} ::  (duration: ${duration}ms)`);
  console.log(JSON.stringify(response.data));
  return response;
};

export const axiosInstance = axios.create({
  baseURL: process.env.MODERN_API_ENDPOINT, // Optional base URL
  timeout: 60 * 1000, // Optional timeout
  headers: {
    'Content-Type': 'application/json',
  },
});

axiosInstance.interceptors.request.use(requestInterceptor);
axiosInstance.interceptors.response.use(responseInterceptor)

@Injectable()
export class MWHttpService implements IHttpClient {

  constructor(private readonly appLogger: AppLogger) { }

  get<T>(url: string): Promise<T> {
    return axiosInstance.get(url);
  }

  post<T, D>(url: string, data: D): Promise<T> {
    return axiosInstance.post(url, data);
  }

  put<T, D>(url: string, data: D): Promise<T> {
    return axiosInstance.put(url, data);
  }

  async makeRequest<T>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response = await axiosInstance.request(config);
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return this.transformExceptions(error);
      } else {
        this.appLogger.error(
          `API Error: ${JSON.stringify(error)}`);
      }
      throw new Error(unKnownError);
    }
  }

  createRequestConfig(
    method: string,//'GET' | 'POST' | 'PUT' | 'DELETE',
    url: string,
    body?: any,
    params?: Record<string, any>,
    headers?: Record<string, string>
  ): AxiosRequestConfig {
    const config: AxiosRequestConfig = {
      method: method,
      url: url,
      params: params || {},  // Query parameters
      data: body || {},      // Body payload for POST/PUT requests
      headers: headers || {} // Optional headers
    };
    return config;
  }

  processRequest<T>(url: string, request: any, headers: HeaderDTO, methodType: string, params?: any): Promise<T> {
    if (request != null) {
      request = this.updateRequestWithDeviceInfo(request, headers);
    }
    const isCardInstance = url.startsWith(process.env.NA_CARD_INSTANCE || "");
    const authorizationToken = isCardInstance
      ? headers.authorization.replace('Bearer ', '')
      : headers.authorization;

    const authHeader: Record<string, string> = {
      Authorization: authorizationToken,
      ...(url.startsWith(process.env.LOGIN_VANITY) && {
        applicationname: headers.applicationname,
        device_id: headers.deviceid,
        version_number: headers.versionnumber,
      }),
      ...(isCardInstance && {
        ApplicationName: headers.applicationname,
        UserName: headers.username,
        skipValidation: request?.skipValidation ?? "",
      }),
    };

    const config = this.createRequestConfig(methodType, url, request, params, authHeader);
    return this.makeRequest(config);
  }

  updateRequestWithDeviceInfo(request: any, headers: HeaderDTO): any {
    request.deviceId = headers.deviceid;
    request.versionNumber = headers.versionnumber;
    request.applicationName = headers.applicationname;
    return request;
  }

  async transformExceptions(error: any): Promise<any> {
    if (error && error?.response && error?.response !== undefined && error?.response?.data !== null) {
      const errorData = typeof error.response.data === 'string'
        ? JSON.parse(error.response.data)
        : error.response.data;
      this.appLogger.error(
        `API Error: ${JSON.stringify(errorData)}`);
      const errorResponse = {
        errorCode: errorData?.errorMetadata?.errorCode || errorData?.errorCode || errorData?.status || errorData?.code || error?.response?.status.toString(),
        errorSummary: errorData?.errorMetadata?.errorSummary || errorData?.errorSummary || errorData?.errorMessage || errorData?.statusMessage || errorData?.error || errorData?.message,
        errorMessage: errorData?.errorMessage || errorData?.errorSummary || errorData?.statusMessage || errorData?.error || errorData?.message,
      }
      throw new DrivenError(errorResponse, error?.response?.status);
    } else {
      throw new Error(unKnownError);
    }
  }
}