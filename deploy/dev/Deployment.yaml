kind: Deployment
apiVersion: apps/v1
metadata:
  name: ci_project_name
  namespace: ci_namespace
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ci_project_name
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: ci_project_name
    spec:
      containers:
        - name: ci_project_name
          image: build_image
          ports:
            - containerPort: 8080
              protocol: TCP
          envFrom:
            - configMapRef:
                name: ci_project_name
            # - secretRef:
            #     name: ci_project_name         
          resources: 
            limits: 
              cpu: 200m 
              memory: 896Mi 
            requests:
              cpu: 200m 
              memory: 896Mi
          readinessProbe:
            httpGet:
              path: /liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            timeoutSeconds: 5
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 2
          livenessProbe:
            httpGet:
              path: /liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 65
            timeoutSeconds: 5
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 2
          imagePullPolicy: IfNotPresent
      restartPolicy: Always

