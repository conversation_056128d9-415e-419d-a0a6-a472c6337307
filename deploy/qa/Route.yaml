kind: Route
apiVersion: route.openshift.io/v1
metadata:
  name: ci_project_name-vanity
  namespace: ci_namespace
spec:
  host: driven-card-mw-qa.fleet.non-prod.fleetcor.com
  path: /
  to:
    kind: Service
    name: ci_project_name
    weight: 100
  port:
    targetPort: 8080
  tls:
    termination: edge
    insecureEdgeTerminationPolicy: Redirect
  wildcardPolicy: None


---
kind: Route
apiVersion: route.openshift.io/v1
metadata:
  name: ci_project_name-utility
  namespace: ci_namespace
spec:
  host: ci_project_name-qa.apps.000-d-ea1-us.ocp.fleetcor.com
  path: /
  to:
    kind: Service
    name: ci_project_name
    weight: 100
  port:
    targetPort: 8080
  tls:
    termination: edge
    insecureEdgeTerminationPolicy: Redirect
  wildcardPolicy: None

