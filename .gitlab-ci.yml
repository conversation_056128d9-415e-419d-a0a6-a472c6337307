workflow:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event" '
      when: never  # Prevent pipeline run for push event
    - when: always # Run pipeline for all other cases

stages:
  - Scan
  - Build
  - Test
  - Deploy
  - Release Approval
  - Change Request

include:
  - project: 'gcto-pipelines/pipeline-templates/templates'  
    ref: 'main'
    file: 'DeveloperPipeline-OpenShift-NodeJS-npm.gitlab-ci.yml'
  - /.gitlab-ci/feature-testing.yml
